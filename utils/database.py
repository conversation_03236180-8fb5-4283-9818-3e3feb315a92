def initialize_database():
    with DatabaseConnection() as conn:
        cursor = conn.cursor()
        
        # Add environment tables
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS environments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                is_active BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS environment_vars (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                env_id INTEGER NOT NULL,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                FOREIGN KEY(env_id) REFERENCES environments(id) ON DELETE CASCADE
            )
        ''')
        
        # Index for faster lookups
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_env_vars ON environment_vars(env_id)')
        conn.commit() 
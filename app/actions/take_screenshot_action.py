from .base_action import BaseAction
import logging
import os
import time
from datetime import datetime

class TakeScreenshotAction(BaseAction):
    """Handler for taking screenshots with custom names"""

    def execute(self, params):
        """
        Execute take screenshot action

        Args:
            params: Dictionary containing:
                - screenshot_name: The name for the screenshot (required)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get screenshot name from parameters
        screenshot_name = params.get('screenshot_name', '').strip()
        if not screenshot_name:
            return {"status": "error", "message": "Screenshot name is required"}

        try:
            # Take screenshot using the controller
            screenshot_path = self.controller.take_screenshot()
            
            if not screenshot_path:
                return {"status": "error", "message": "Failed to take screenshot"}

            # Create the new filename with the custom name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            new_filename = f"app-screenshot_{screenshot_name}_{timestamp}.png"
            
            # Get the directory of the original screenshot
            screenshot_dir = os.path.dirname(screenshot_path)
            new_path = os.path.join(screenshot_dir, new_filename)
            
            # Rename the screenshot file
            if os.path.exists(screenshot_path):
                os.rename(screenshot_path, new_path)
                self.logger.info(f"Screenshot saved as: {new_filename}")
                return {
                    "status": "success", 
                    "message": f"Screenshot taken and saved as {new_filename}",
                    "screenshot_path": new_path
                }
            else:
                return {"status": "error", "message": "Screenshot file not found after capture"}

        except Exception as e:
            self.logger.error(f"Error taking screenshot: {e}")
            return {"status": "error", "message": f"Screenshot action failed: {str(e)}"}

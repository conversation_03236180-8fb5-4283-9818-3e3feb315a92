import os
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

def get_reports_directory():
    """
    Get the reports directory path consistently across the application.
    
    Returns:
        str: The absolute path to the reports directory
    """
    try:
        # First try to get from directory_paths_db
        try:
            import sys
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
            from directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if reports_dir:
                logger.info(f"Using reports directory from database: {reports_dir}")
                # Make sure it's an absolute path
                if not os.path.isabs(reports_dir):
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    reports_dir = os.path.join(base_dir, reports_dir)
                return reports_dir
        except Exception as e:
            logger.error(f"Error getting reports directory from database: {str(e)}")
        
        # Next try to get from config.json
        try:
            config_path = Path(__file__).resolve().parent.parent / 'config.json'
            if config_path.exists():
                with open(config_path, 'r') as f:
                    config_data = json.load(f)
                    reports_dir = config_data.get('reports_dir')
                    if reports_dir:
                        logger.info(f"Using reports directory from config.json: {reports_dir}")
                        # Make sure it's an absolute path
                        if not os.path.isabs(reports_dir):
                            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                            reports_dir = os.path.join(base_dir, reports_dir)
                        return reports_dir
        except Exception as e:
            logger.error(f"Error getting reports directory from config.json: {str(e)}")
        
        # Fallback to default
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        reports_dir = os.path.join(base_dir, 'reports')
        logger.info(f"Using default reports directory: {reports_dir}")
        return reports_dir
    except Exception as e:
        logger.error(f"Error in get_reports_directory: {str(e)}")
        # Ultimate fallback
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        return os.path.join(base_dir, 'reports')

class TestSuitesManager {
    constructor() {
        this.selectedTestCases = [];  // Changed from Set to Array to maintain order
        this.allTestCases = [];  // Store all test cases for the selector
        this.sortable = null;  // Reference to Sortable instance
        this.initializeEventListeners();
        this.loadTestCases();
        this.loadTestSuites();
    }

    /**
     * Auto-save functionality:
     * - Automatically saves test suite changes when test cases are added/removed/reordered
     * - Saves changes when name or description fields are modified (with 1-second debounce)
     * - Only works when editing an existing test suite (not during creation)
     * - Shows visual feedback for save status
     */

    initializeEventListeners() {
        // Save Test Suite button click handler
        document.getElementById('saveTestSuiteBtn').addEventListener('click', () => this.saveTestSuite());

        // Auto-save when name or description changes (with debouncing)
        this.setupAutoSaveOnFieldChange();

        // Test case checkbox change handler (keep for backward compatibility)
        document.getElementById('availableTestCases').addEventListener('change', async (e) => {
            if (e.target.type === 'checkbox') {
                const testCaseId = e.target.value;
                if (e.target.checked) {
                    if (!this.selectedTestCases.includes(testCaseId)) {
                        this.selectedTestCases.push(testCaseId);
                    }
                } else {
                    this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);
                }
                this.updateSelectedTestCasesList();

                // Auto-save the changes if we're in edit mode
                await this.autoSaveTestSuiteChanges();
            }
        });

        // Add test case button click handler
        const addTestCaseBtn = document.getElementById('addTestCaseBtn');
        if (addTestCaseBtn) {
            addTestCaseBtn.addEventListener('click', () => {
                const selector = document.getElementById('testCaseSelector');
                const selectedValue = selector.value;

                if (selectedValue && !this.selectedTestCases.includes(selectedValue)) {
                    this.selectedTestCases.push(selectedValue);
                    this.updateSelectedTestCasesList();

                    // Reset the selector
                    selector.value = '';
                }
            });
        }
    }

    async loadTestCases() {
        try {
            const response = await fetch('/api/recording/list');
            const data = await response.json();

            if (data.status === 'success') {
                this.allTestCases = data.test_cases; // Store all test cases
                this.displayTestCases(data.test_cases);
                this.populateTestCaseSelector(data.test_cases);
            } else {
                console.error('Failed to load test cases:', data.error);
                this.showError('Failed to load test cases');
            }
        } catch (error) {
            console.error('Error loading test cases:', error);
            this.showError('Error loading test cases');
        }
    }

    displayTestCases(testCases) {
        const container = document.getElementById('availableTestCases');
        container.innerHTML = '';

        testCases.forEach(testCase => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

            // Create label badges HTML if labels exist
            const labelsHtml = testCase.labels && testCase.labels.length > 0 
                ? testCase.labels.map(label => 
                    `<span class="badge bg-secondary me-1">${label}</span>`
                ).join('') 
                : '';

            listItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="form-check">
                        <input class="form-check-input me-3" type="checkbox" value="${testCase.filename}" id="testCase_${testCase.filename}">
                        <label class="form-check-label" for="testCase_${testCase.filename}">
                            <span class="fw-bold">${testCase.name}</span>
                            <div class="mt-1">${labelsHtml}</div>
                        </label>
                    </div>
                </div>
                <span class="badge bg-light text-dark">Created: ${new Date(testCase.created).toLocaleDateString()}</span>
            `;

            container.appendChild(listItem);
        });
    }

    populateTestCaseSelector(testCases) {
        const selector = document.getElementById('testCaseSelector');
        if (!selector) return;

        // Clear existing options except the first placeholder
        while (selector.options.length > 1) {
            selector.remove(1);
        }

        // Sort test cases by name for easier selection
        const sortedTestCases = [...testCases].sort((a, b) =>
            (a.name || '').localeCompare(b.name || '')
        );

        // Add options for each test case
        sortedTestCases.forEach(testCase => {
            const option = document.createElement('option');
            option.value = testCase.filename;
            option.textContent = testCase.name;
            selector.appendChild(option);
        });
    }

    updateSelectedTestCasesList() {
        const container = document.getElementById('selectedTestCases');

        // Destroy existing Sortable instance if it exists
        if (this.sortable) {
            this.sortable.destroy();
            this.sortable = null;
        }

        container.innerHTML = '';

        this.selectedTestCases.forEach(testCaseId => {
            // Find test case info from our stored test cases
            const testCaseInfo = this.allTestCases.find(tc => tc.filename === testCaseId);

            const div = document.createElement('div');
            div.className = 'list-group-item d-flex justify-content-between align-items-center';
            div.dataset.testCaseId = testCaseId;

            // If we found the test case in our list, use its name
            if (testCaseInfo) {
                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${testCaseInfo.name || 'Unknown Test Case'}</span>
                        ${testCaseInfo.labels && testCaseInfo.labels.length > 0 ? 
                            `<div class="ms-2">
                                ${testCaseInfo.labels.map(label => 
                                    `<span class="badge bg-secondary me-1">${label}</span>`
                                ).join('')}
                            </div>` : ''}
                    </div>
                    <button type="button" class="btn btn-sm btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')">
                        <i class="bi bi-x"></i>
                    </button>
                `;
            } else {
                // Fallback for test cases not found in the list
                const checkbox = document.getElementById(`testCase_${testCaseId}`);
                let displayName;

                if (checkbox && checkbox.nextElementSibling) {
                    displayName = checkbox.nextElementSibling.textContent.trim();
                } else {
                    // Extract a simple name from the filename if possible
                    displayName = testCaseId.split('_')[0] || testCaseId;
                }

                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${displayName} <small class="text-muted">(ID: ${testCaseId})</small></span>
                    </div>
                    <button type="button" class="btn btn-sm btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')">
                        <i class="bi bi-x"></i>
                    </button>
                `;
            }

            container.appendChild(div);
        });

        // Initialize Sortable on the container
        if (container.children.length > 0) {
            this.initSortable(container);
        }

        // Update checkboxes to match selected state
        document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = this.selectedTestCases.includes(checkbox.value);
        });
    }

    initSortable(container) {
        // Make sure Sortable.js is available
        if (typeof Sortable !== 'undefined') {
            this.sortable = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',

                // Update the array when items are reordered
                onEnd: async (evt) => {
                    // Get the new order of test cases
                    const items = Array.from(container.children);
                    this.selectedTestCases = items.map(item => item.dataset.testCaseId);

                    // Auto-save the changes if we're in edit mode
                    await this.autoSaveTestSuiteChanges();
                }
            });
        } else {
            console.error('Sortable.js is not loaded. Drag and drop functionality will not work.');
        }
    }

    async removeTestCase(testCaseId) {
        // Remove from array
        this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);

        // Uncheck the checkbox if it exists
        const checkbox = document.getElementById(`testCase_${testCaseId}`);
        if (checkbox) {
            checkbox.checked = false;
        }

        // Update the UI
        this.updateSelectedTestCasesList();

        // Auto-save the changes if we're in edit mode
        await this.autoSaveTestSuiteChanges();
    }

    async autoSaveTestSuiteChanges() {
        // Check if we're currently editing a test suite
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEditMode = saveButton && saveButton.dataset.mode === 'edit';

        if (!isEditMode) {
            // Not in edit mode, no need to auto-save
            return;
        }

        const suiteId = saveButton.dataset.suiteId;
        if (!suiteId) {
            console.warn('No suite ID found for auto-save');
            return;
        }

        try {
            // Show auto-save indicator
            this.showAutoSaveIndicator('Saving...');

            // Get current form values
            const name = document.getElementById('testSuiteName').value;
            const description = document.getElementById('testSuiteDescription').value;

            // Validate that we have required data
            if (!name) {
                // Don't auto-save if name is missing
                this.hideAutoSaveIndicator();
                return;
            }

            // Allow auto-save even with no test cases during editing
            // The validation for minimum test cases will happen during execution

            // Save the changes to the server
            const response = await fetch(`/api/test_suites/${suiteId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                console.log('Test suite auto-saved successfully');
                // Show a subtle success indicator
                this.showAutoSaveIndicator('✓ Saved', 'success');
            } else {
                console.error('Auto-save failed:', data.error);
                this.showAutoSaveIndicator('✗ Save failed', 'error');
                this.showError('Failed to auto-save changes: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error during auto-save:', error);
            this.showAutoSaveIndicator('✗ Save failed', 'error');
            this.showError('Error auto-saving changes: ' + error.message);
        }
    }

    showAutoSaveIndicator(message, type = 'info') {
        // Show auto-save indicator next to the save button
        const saveButton = document.getElementById('saveTestSuiteBtn');
        if (!saveButton) return;

        // Remove any existing indicator
        this.hideAutoSaveIndicator();

        // Create indicator element
        const indicator = document.createElement('span');
        indicator.id = 'autoSaveIndicator';
        indicator.className = 'ms-2 small';
        indicator.textContent = message;

        // Style based on type
        switch (type) {
            case 'success':
                indicator.className += ' text-success';
                break;
            case 'error':
                indicator.className += ' text-danger';
                break;
            case 'info':
            default:
                indicator.className += ' text-muted';
                break;
        }

        // Insert after the save button
        saveButton.parentNode.insertBefore(indicator, saveButton.nextSibling);

        // Auto-hide success/error messages after 3 seconds
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                this.hideAutoSaveIndicator();
            }, 3000);
        }
    }

    hideAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.remove();
        }
    }

    setupAutoSaveOnFieldChange() {
        // Debounce timer for auto-save
        let autoSaveTimer = null;

        const debouncedAutoSave = () => {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(async () => {
                await this.autoSaveTestSuiteChanges();
            }, 1000); // Wait 1 second after user stops typing
        };

        // Add event listeners to name and description fields
        const nameField = document.getElementById('testSuiteName');
        const descriptionField = document.getElementById('testSuiteDescription');

        if (nameField) {
            nameField.addEventListener('input', debouncedAutoSave);
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', debouncedAutoSave);
        }
    }

    async loadTestSuites() {
        try {
            const response = await fetch('/api/test_suites/list');
            const data = await response.json();

            if (data.status === 'success') {
                this.displayTestSuites(data.test_suites);
            } else {
                console.error('Failed to load test suites:', data.error);
                this.showError('Failed to load test suites');
            }
        } catch (error) {
            console.error('Error loading test suites:', error);
            this.showError('Error loading test suites');
        }
    }

    displayTestSuites(testSuites) {
        const testSuitesList = document.getElementById('testSuitesList');
        testSuitesList.innerHTML = ''; // Clear existing list

        if (testSuites.length === 0) {
            testSuitesList.innerHTML = '<p class="text-muted">No test suites created yet.</p>';
            return;
        }

        testSuites.forEach(suite => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item'; // Use the new list item class

            const testCaseCount = suite.test_cases ? suite.test_cases.length : 0;
            const created = new Date(suite.created).toLocaleDateString();

            listItem.innerHTML = `
                <div class="test-suite-info">
                    <span class="test-suite-name">${suite.name}</span>
                    <div class="test-suite-meta">
                        <span><i class="bi bi-file-earmark-text"></i> ${testCaseCount} Test Case${testCaseCount !== 1 ? 's' : ''}</span>
                        <span class="ms-3"><i class="bi bi-calendar-event"></i> Created: ${created}</span>
                    </div>
                </div>
                <div class="test-suite-actions">
                    <button class="btn btn-sm btn-outline-primary edit-suite-btn" data-suite-id="${suite.id}" title="Edit Test Suite">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-secondary rename-suite-btn" data-suite-id="${suite.id}" data-suite-name="${suite.name}" title="Rename Test Suite">
                        <i class="bi bi-pencil-square"></i> Rename
                    </button>
                    <button class="btn btn-sm btn-outline-secondary duplicate-suite-btn" data-suite-id="${suite.id}" title="Duplicate Test Suite">
                        <i class="bi bi-files"></i> Duplicate
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-suite-btn" data-suite-id="${suite.id}" title="Delete Test Suite">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            `;

            testSuitesList.appendChild(listItem);
        });

        // Re-add event listeners for the new buttons
        this.addTestSuiteActionListeners();
    }

    addTestSuiteActionListeners() {
        const editButtons = document.querySelectorAll('.edit-suite-btn');
        const renameButtons = document.querySelectorAll('.rename-suite-btn');
        const duplicateButtons = document.querySelectorAll('.duplicate-suite-btn');
        const deleteButtons = document.querySelectorAll('.delete-suite-btn');

        editButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.editTestSuite(suiteId);
            });
        });

        renameButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                const currentName = event.currentTarget.dataset.suiteName;
                this.renameTestSuite(suiteId, currentName);
            });
        });

        duplicateButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.duplicateTestSuite(suiteId);
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                if (confirm(`Are you sure you want to delete test suite ${suiteId}?`)) {
                    this.deleteTestSuite(suiteId);
                }
            });
        });
    }

    async saveTestSuite() {
        const name = document.getElementById('testSuiteName').value;
        const description = document.getElementById('testSuiteDescription').value;
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEdit = saveButton.dataset.mode === 'edit';
        const suiteId = isEdit ? saveButton.dataset.suiteId : null;

        if (!name) {
            this.showError('Please enter a name for the test suite');
            return;
        }

        if (this.selectedTestCases.length === 0) {
            this.showError('Please select at least one test case');
            return;
        }

        try {
            const endpoint = isEdit
                ? `/api/test_suites/${suiteId}/update`
                : '/api/test_suites/create';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases // Already an array, no need for Array.from
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Close modal and reset form
                const modal = bootstrap.Modal.getInstance(document.getElementById('createTestSuiteModal'));
                modal.hide();
                document.getElementById('createTestSuiteForm').reset();
                this.selectedTestCases = []; // Reset to empty array
                this.updateSelectedTestCasesList();

                // Refresh test suites list
                this.loadTestSuites();

                // Uncheck all checkboxes
                document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Reset button text and mode for next time
                saveButton.textContent = 'Save Test Suite';
                saveButton.dataset.mode = 'create';
                delete saveButton.dataset.suiteId;

                // Reset modal title
                document.getElementById('createTestSuiteModalLabel').textContent = 'Create New Test Suite';

                // Hide any auto-save indicators
                this.hideAutoSaveIndicator();

                this.showSuccess(isEdit ? 'Test suite updated successfully' : 'Test suite created successfully');
            } else {
                this.showError(data.error || 'Failed to save test suite');
            }
        } catch (error) {
            console.error('Error saving test suite:', error);
            this.showError('Error saving test suite');
        }
    }

    async renameTestSuite(suiteId, currentName) {
        // Prompt for new name
        const newName = prompt('Enter a new name for the test suite:', currentName);

        // Check if user cancelled or entered an empty name
        if (!newName || newName.trim() === '') {
            return;
        }

        try {
            // Use the existing update endpoint with just the name change
            const response = await fetch(`/api/test_suites/${suiteId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name: newName,
                    // We need to include test_cases to satisfy the API validation
                    // Fetch the current test suite to get its test cases
                    test_cases: (await this.getTestSuite(suiteId)).test_cases
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite renamed successfully');
            } else {
                this.showError(data.error || 'Failed to rename test suite');
            }
        } catch (error) {
            console.error('Error renaming test suite:', error);
            this.showError('Error renaming test suite');
        }
    }

    async duplicateTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}/duplicate`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite duplicated successfully');
            } else {
                this.showError(data.error || 'Failed to duplicate test suite');
            }
        } catch (error) {
            console.error('Error duplicating test suite:', error);
            this.showError('Error duplicating test suite');
        }
    }

    async getTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status === 'success') {
                return data.test_suite;
            } else {
                this.showError(data.error || 'Failed to fetch test suite');
                return null;
            }
        } catch (error) {
            console.error('Error fetching test suite:', error);
            this.showError('Error fetching test suite');
            return null;
        }
    }

    async deleteTestSuite(suiteId) {
        if (!confirm('Are you sure you want to delete this test suite?')) {
            return;
        }

        try {
            const response = await fetch(`/api/test_suites/${suiteId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite deleted successfully');
            } else {
                this.showError(data.error || 'Failed to delete test suite');
            }
        } catch (error) {
            console.error('Error deleting test suite:', error);
            this.showError('Error deleting test suite');
        }
    }

    async runTestSuite(suiteId) {
        try {
            // First, fetch the test suite details to get the test cases
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status !== 'success') {
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            // Confirm with the user
            if (!confirm(`Run all ${data.test_suite.test_cases.length} test cases in this suite?`)) {
                return;
            }

            // Show loading indicator
            this.showLoading('Running test suite...');

            // Call the API to run the test suite
            const runResponse = await fetch(`/api/test_suites/${suiteId}/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    device_id: window.app ? window.app.deviceId : null  // Include device ID for session isolation
                })
            });

            // Hide loading indicator
            this.hideLoading();

            const runData = await runResponse.json();

            if (runData.status === 'success') {
                this.showSuccess('Test suite execution started');

                // If a report URL is returned, offer to open it
                if (runData.report_url) {
                    // Add a button to open the report
                    this.showReportButton(runData.report_url, data.test_suite.name);

                    // Refresh the Reports tab if it exists
                    if (window.reportsManager) {
                        window.reportsManager.loadReports();
                    }
                }

                // Optionally, switch to a results tab or show execution status
                const testCasesTab = document.getElementById('test-cases-tab-btn');
                if (testCasesTab) {
                    const tabInstance = new bootstrap.Tab(testCasesTab);
                    tabInstance.show();
                }
            } else {
                this.showError(runData.error || 'Failed to run test suite');
            }
        } catch (error) {
            console.error('Error running test suite:', error);
            this.showError('Error running test suite');
            this.hideLoading();
        }
    }

    async editTestSuite(suiteId) {
        try {
            // Fetch the test suite details
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status !== 'success') {
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            const suite = data.test_suite;

            // Populate the edit form
            document.getElementById('testSuiteName').value = suite.name;
            document.getElementById('testSuiteDescription').value = suite.description || '';

            // Clear existing selections
            this.selectedTestCases = [];

            // Ensure all available checkboxes are unchecked first
            document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Process the test cases in this suite (maintain order)
            if (Array.isArray(suite.test_cases)) {
                suite.test_cases.forEach(testCaseData => {
                    let filename = null;

                    // Determine the filename based on data structure
                    if (typeof testCaseData === 'string') {
                        filename = testCaseData;
                    } else if (testCaseData && typeof testCaseData.filename === 'string') {
                        filename = testCaseData.filename;
                    }

                    // Process if we have a valid filename
                    if (filename) {
                        // Add to our array if not already present
                        if (!this.selectedTestCases.includes(filename)) {
                            this.selectedTestCases.push(filename);
                        }

                        // Check the checkbox if it exists
                        const checkbox = document.getElementById(`testCase_${filename}`);
                        if (checkbox) {
                            checkbox.checked = true;
                        } else {
                            console.warn(`Checkbox for test case filename ${filename} not found in the available list.`);
                        }
                    } else {
                        console.warn('Skipping invalid test case data during edit:', testCaseData);
                    }
                });
            }

            // Update the selected test cases list
            this.updateSelectedTestCasesList();

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('createTestSuiteModal'));
            modal.show();

            // Change the title to indicate editing
            document.getElementById('createTestSuiteModalLabel').textContent = 'Edit Test Suite';

            // Change the save button text
            const saveButton = document.getElementById('saveTestSuiteBtn');
            saveButton.textContent = 'Update Test Suite';
            saveButton.dataset.mode = 'edit';
            saveButton.dataset.suiteId = suiteId;
        } catch (error) {
            console.error('Error editing test suite:', error);
            this.showError('Error editing test suite');
        }
    }

    showError(message) {
        // Display error message using Bootstrap toast or alert
        alert(message);
    }

    showSuccess(message) {
        // Display success message using Bootstrap toast or alert
        alert(message);
    }

    showLoading(message) {
        // Implement loading indicator if needed
        console.log(`Loading: ${message}`);
    }

    hideLoading() {
        // Hide loading indicator if needed
        console.log('Loading complete');
    }

    /**
     * Show a button to open the test report
     *
     * @param {string} reportUrl - URL to the test report
     * @param {string} suiteName - Name of the test suite
     */
    showReportButton(reportUrl, suiteName) {
        // Remove any existing report notification
        const existingContainer = document.getElementById('report-notification-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Create notification container
        const container = document.createElement('div');
        container.id = 'report-notification-container';
        container.className = 'position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1050';

        // Create a Bootstrap toast
        container.innerHTML = `
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="bi bi-file-earmark-check me-2"></i>
                    <strong class="me-auto">Test Report Ready</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    <div class="mb-2">Test suite "${suiteName}" execution report is ready.</div>
                    <div class="d-flex justify-content-between">
                        <a href="${reportUrl}" target="_blank" class="btn btn-primary btn-sm">
                            <i class="bi bi-eye me-1"></i> View Report
                        </a>
                    </div>
                </div>
            </div>
        `;

        // Add to document body
        document.body.appendChild(container);

        // Set up auto-dismiss after 30 seconds
        setTimeout(() => {
            const toast = container.querySelector('.toast');
            if (toast) {
                const bsToast = bootstrap.Toast.getInstance(toast);
                if (bsToast) {
                    bsToast.hide();
                } else {
                    container.remove();
                }
            }
        }, 30000);

        // Set up event listeners
        const closeBtn = container.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                container.remove();
            });
        }
    }

    showLoading(message = 'Loading...') {
        let loadingOverlay = document.getElementById('loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loading-overlay';
            loadingOverlay.style.position = 'fixed';
            loadingOverlay.style.top = '0';
            loadingOverlay.style.left = '0';
            loadingOverlay.style.width = '100%';
            loadingOverlay.style.height = '100%';
            loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loadingOverlay.style.zIndex = '9999';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.justifyContent = 'center';
            loadingOverlay.style.alignItems = 'center';
            loadingOverlay.style.color = '#fff';
            loadingOverlay.style.fontSize = '1.5rem';

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border mr-3';
            spinner.setAttribute('role', 'status');

            const loadingText = document.createElement('span');
            loadingText.id = 'loading-message';
            loadingText.style.marginLeft = '10px';

            loadingOverlay.appendChild(spinner);
            loadingOverlay.appendChild(loadingText);

            document.body.appendChild(loadingOverlay);
        }

        document.getElementById('loading-message').textContent = message;
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.testSuitesManager = new TestSuitesManager();
});
Action Log - 2025-06-16 09:36:11
================================================================================

[[09:36:11]] [INFO] Generating execution report...
[[09:36:11]] [SUCCESS] All tests passed successfully!
[[09:36:11]] [WARNING] Execution stopped by user.
[[09:36:11]] [ERROR] Action 1 failed: Element with xpath '//XCUIElementTypeTextField[@name="Email"]' not found within timeout of 10.0 seconds
[[09:36:09]] [WARNING] Stop requested. Finishing current action...
[[09:35:59]] [INFO] Executing action 1/8: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:35:59]] [INFO] ExecutionManager: Starting execution of 8 actions...
[[09:35:59]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[09:35:59]] [INFO] Clearing screenshots from database before execution...
[[09:35:59]] [SUCCESS] All screenshots deleted successfully
[[09:35:59]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:35:59]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_093559/screenshots
[[09:35:59]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_093559
[[09:35:59]] [SUCCESS] Report directory initialized successfully
[[09:35:59]] [INFO] Initializing report directory and screenshots folder...
[[09:35:53]] [SUCCESS] All screenshots deleted successfully
[[09:35:53]] [SUCCESS] Loaded test case "Kmart-NZ-Signin" with 8 actions
[[09:35:53]] [SUCCESS] Added action: exists
[[09:35:53]] [SUCCESS] Added action: iosFunctions
[[09:35:53]] [SUCCESS] Added action: tap
[[09:35:53]] [SUCCESS] Added action: tapOnText
[[09:35:53]] [SUCCESS] Added action: tap
[[09:35:53]] [SUCCESS] Added action: text
[[09:35:53]] [SUCCESS] Added action: tap
[[09:35:53]] [SUCCESS] Added action: waitTill
[[09:35:53]] [INFO] All actions cleared
[[09:35:53]] [INFO] Cleaning up screenshots...
[[09:35:53]] [WARNING] Device 00008030-00020C123E60402E not found in available devices
[[09:35:35]] [SUCCESS] Screenshot refreshed successfully
[[09:35:34]] [SUCCESS] Screenshot refreshed
[[09:35:34]] [INFO] Refreshing screenshot...
[[09:35:33]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[09:35:33]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[09:35:26]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[09:35:22]] [SUCCESS] Found 1 device(s)
[[09:35:21]] [INFO] Refreshing device list...

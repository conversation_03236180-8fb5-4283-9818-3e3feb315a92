<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/16/2025, 9:36:11 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 16/06/2025, 09:36:11
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="8 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Test Case
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3169ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">5372ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="unknown"
                            data-screenshot="kmartprod0.png" data-action-id="kmartprod0" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Input text: "<EMAIL>" <span class="action-id-badge" title="Action ID: kmartprod0">kmartprod0</span>
                            </div>
                            <span class="test-step-duration">2744ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="unknown"
                            data-screenshot="EELcfo48Sh.png" data-action-id="EELcfo48Sh" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on image: keyboard_done_iphoneSE.png <span class="action-id-badge" title="Action ID: EELcfo48Sh">EELcfo48Sh</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on Text: "Continue" <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">5184ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="placeholder_report.png" data-action-id="placeholder_report" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: placeholder_report">placeholder_report</span>
                            </div>
                            <span class="test-step-duration">3988ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="unknown"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3414ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 16/06/2025, 09:36:11","testCases":[{"name":"Test Case","status":"passed","steps":[{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"failed","duration":"3169ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Email\"]","status":"unknown","duration":"5372ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Input text: \"<EMAIL>\"","status":"unknown","duration":"2744ms","action_id":"kmartprod0","screenshot_filename":"kmartprod0.png","report_screenshot":"kmartprod0.png","resolved_screenshot":"screenshots/kmartprod0.png","action_id_screenshot":"screenshots/kmartprod0.png"},{"name":"Tap on image: keyboard_done_iphoneSE.png","status":"unknown","duration":"0ms","action_id":"EELcfo48Sh","screenshot_filename":"EELcfo48Sh.png","report_screenshot":"EELcfo48Sh.png","resolved_screenshot":"screenshots/EELcfo48Sh.png","clean_action_id":"EELcfo48Sh","prefixed_action_id":"al_EELcfo48Sh","action_id_screenshot":"screenshots/EELcfo48Sh.png"},{"name":"Tap on Text: \"Continue\"","status":"unknown","duration":"0ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]","status":"unknown","duration":"5184ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text","status":"unknown","duration":"3988ms","action_id":"placeholder_report","screenshot_filename":"placeholder_report.png","report_screenshot":"placeholder_report.png","resolved_screenshot":"screenshots/placeholder_report.png","clean_action_id":"placeholder_report","prefixed_action_id":"al_placeholder_report","action_id_screenshot":"screenshots/placeholder_report.png"},{"name":"Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists","status":"unknown","duration":"3414ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]}],"passed":1,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["EELcfo48Sh.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>
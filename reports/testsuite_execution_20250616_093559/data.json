{"name": "UI Execution 16/06/2025, 09:36:11", "testCases": [{"name": "Test Case", "status": "passed", "steps": [{"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "failed", "duration": "3169ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeTextField[@name=\"Em<PERSON>\"]", "status": "unknown", "duration": "5372ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "unknown", "duration": "2744ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on image: keyboard_done_iphoneSE.png", "status": "unknown", "duration": "0ms", "action_id": "EELcfo48Sh", "screenshot_filename": "EELcfo48Sh.png", "report_screenshot": "EELcfo48Sh.png", "resolved_screenshot": "screenshots/EELcfo48Sh.png", "clean_action_id": "EELcfo48Sh", "prefixed_action_id": "al_EELcfo48Sh", "action_id_screenshot": "screenshots/EELcfo48Sh.png"}, {"name": "Tap on Text: \"Continue\"", "status": "unknown", "duration": "0ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report"}, {"name": "Tap on element with xpath: //XCUIElementTypeSecureTextField[@name=\"Password\"]", "status": "unknown", "duration": "5184ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "unknown", "duration": "3988ms", "action_id": "placeholder_report", "screenshot_filename": "placeholder_report.png", "report_screenshot": "placeholder_report.png", "resolved_screenshot": "screenshots/placeholder_report.png", "clean_action_id": "placeholder_report", "prefixed_action_id": "al_placeholder_report"}, {"name": "Check if element with xpath=\"//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]\" exists", "status": "unknown", "duration": "3414ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}], "passed": 1, "failed": 0, "skipped": 0, "status": "passed"}
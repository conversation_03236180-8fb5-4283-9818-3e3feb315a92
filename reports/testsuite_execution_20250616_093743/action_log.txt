Action Log - 2025-06-16 09:39:19
================================================================================

[[09:39:19]] [INFO] Generating execution report...
[[09:39:19]] [SUCCESS] All tests passed successfully!
[[09:39:19]] [WARNING] Execution stopped by user.
[[09:39:19]] [ERROR] Error executing action 8: Failed to fetch
[[09:39:10]] [WARNING] Stop requested. Finishing current action...
[[09:38:49]] [INFO] Executing action 8/48: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:38:49]] [ERROR] Action 7 failed: Failed to input text: Uno card
[[09:38:47]] [SUCCESS] Screenshot refreshed successfully
[[09:38:47]] [SUCCESS] Screenshot refreshed successfully
[[09:38:46]] [INFO] Executing action 7/48: iOS Function: text
[[09:38:45]] [SUCCESS] Screenshot refreshed
[[09:38:45]] [INFO] Refreshing screenshot...
[[09:38:42]] [SUCCESS] Screenshot refreshed successfully
[[09:38:42]] [SUCCESS] Screenshot refreshed successfully
[[09:38:40]] [INFO] Executing action 6/48: Tap on image: homepage-search-se.png
[[09:38:40]] [SUCCESS] Screenshot refreshed
[[09:38:40]] [INFO] Refreshing screenshot...
[[09:38:40]] [SUCCESS] Screenshot refreshed
[[09:38:40]] [INFO] Refreshing screenshot...
[[09:38:34]] [SUCCESS] Screenshot refreshed successfully
[[09:38:34]] [SUCCESS] Screenshot refreshed successfully
[[09:38:33]] [INFO] Executing Multi Step action step 8/8: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[09:38:33]] [SUCCESS] Screenshot refreshed
[[09:38:33]] [INFO] Refreshing screenshot...
[[09:38:29]] [SUCCESS] Screenshot refreshed successfully
[[09:38:29]] [SUCCESS] Screenshot refreshed successfully
[[09:38:29]] [INFO] Executing Multi Step action step 7/8: iOS Function: text
[[09:38:28]] [SUCCESS] Screenshot refreshed
[[09:38:28]] [INFO] Refreshing screenshot...
[[09:38:25]] [SUCCESS] Screenshot refreshed successfully
[[09:38:25]] [SUCCESS] Screenshot refreshed successfully
[[09:38:25]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:38:24]] [SUCCESS] Screenshot refreshed
[[09:38:24]] [INFO] Refreshing screenshot...
[[09:38:19]] [SUCCESS] Screenshot refreshed successfully
[[09:38:19]] [SUCCESS] Screenshot refreshed successfully
[[09:38:19]] [INFO] Executing Multi Step action step 5/8: Tap on Text: "Continue"
[[09:38:18]] [SUCCESS] Screenshot refreshed
[[09:38:18]] [INFO] Refreshing screenshot...
[[09:38:14]] [SUCCESS] Screenshot refreshed successfully
[[09:38:14]] [SUCCESS] Screenshot refreshed successfully
[[09:38:14]] [INFO] Executing Multi Step action step 4/8: Tap on image: keyboard_done_iphoneSE.png
[[09:38:14]] [SUCCESS] Screenshot refreshed
[[09:38:14]] [INFO] Refreshing screenshot...
[[09:38:11]] [SUCCESS] Screenshot refreshed successfully
[[09:38:11]] [SUCCESS] Screenshot refreshed successfully
[[09:38:11]] [INFO] Executing Multi Step action step 3/8: Input text: "<EMAIL>"
[[09:38:10]] [SUCCESS] Screenshot refreshed
[[09:38:10]] [INFO] Refreshing screenshot...
[[09:38:07]] [SUCCESS] Screenshot refreshed successfully
[[09:38:07]] [SUCCESS] Screenshot refreshed successfully
[[09:38:06]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:38:06]] [SUCCESS] Screenshot refreshed
[[09:38:06]] [INFO] Refreshing screenshot...
[[09:38:03]] [SUCCESS] Screenshot refreshed successfully
[[09:38:03]] [SUCCESS] Screenshot refreshed successfully
[[09:38:03]] [INFO] Executing Multi Step action step 1/8: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:38:03]] [INFO] Loaded 8 steps from test case: Kmart-NZ-Signin
[[09:38:03]] [INFO] Loading steps for Multi Step action: Kmart-NZ-Signin
[[09:38:03]] [INFO] Executing action 5/48: Execute Test Case: Kmart-NZ-Signin (8 steps)
[[09:38:02]] [SUCCESS] Screenshot refreshed
[[09:38:02]] [INFO] Refreshing screenshot...
[[09:37:59]] [SUCCESS] Screenshot refreshed successfully
[[09:37:59]] [SUCCESS] Screenshot refreshed successfully
[[09:37:59]] [INFO] Executing action 4/48: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:37:58]] [SUCCESS] Screenshot refreshed
[[09:37:58]] [INFO] Refreshing screenshot...
[[09:37:56]] [SUCCESS] Screenshot refreshed successfully
[[09:37:56]] [SUCCESS] Screenshot refreshed successfully
[[09:37:56]] [INFO] Executing action 3/48: iOS Function: alert_accept
[[09:37:55]] [SUCCESS] Screenshot refreshed
[[09:37:55]] [INFO] Refreshing screenshot...
[[09:37:49]] [SUCCESS] Screenshot refreshed successfully
[[09:37:49]] [SUCCESS] Screenshot refreshed successfully
[[09:37:49]] [INFO] Executing action 2/48: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:37:48]] [SUCCESS] Screenshot refreshed
[[09:37:48]] [INFO] Refreshing screenshot...
[[09:37:43]] [INFO] Executing action 1/48: Restart app: nz.com.kmart
[[09:37:43]] [INFO] ExecutionManager: Starting execution of 48 actions...
[[09:37:43]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[09:37:43]] [INFO] Clearing screenshots from database before execution...
[[09:37:43]] [SUCCESS] All screenshots deleted successfully
[[09:37:43]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:37:43]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_093743/screenshots
[[09:37:43]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_093743
[[09:37:43]] [SUCCESS] Report directory initialized successfully
[[09:37:43]] [INFO] Initializing report directory and screenshots folder...
[[09:37:39]] [SUCCESS] All screenshots deleted successfully
[[09:37:39]] [SUCCESS] Loaded test case "WishList NZ" with 48 actions
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tapOnText
[[09:37:39]] [SUCCESS] Added action: ifElseSteps
[[09:37:39]] [SUCCESS] Added action: tapOnText
[[09:37:39]] [SUCCESS] Added action: ifElseSteps
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tapOnText
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tapOnText
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: swipeTillVisible
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: swipe
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: swipeTillVisible
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: iosFunctions
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: swipeTillVisible
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: swipe
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: iosFunctions
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: multiStep
[[09:37:39]] [SUCCESS] Added action: waitTill
[[09:37:39]] [SUCCESS] Added action: iosFunctions
[[09:37:39]] [SUCCESS] Added action: tap
[[09:37:39]] [SUCCESS] Added action: restartApp
[[09:37:39]] [INFO] All actions cleared
[[09:37:39]] [INFO] Cleaning up screenshots...
[[09:37:39]] [WARNING] Device 00008030-00020C123E60402E not found in available devices
[[09:37:13]] [SUCCESS] Screenshot refreshed successfully
[[09:37:12]] [SUCCESS] Screenshot refreshed
[[09:37:12]] [INFO] Refreshing screenshot...
[[09:37:11]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[09:37:11]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[09:37:04]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[09:37:02]] [SUCCESS] Found 1 device(s)
[[09:37:01]] [INFO] Refreshing device list...

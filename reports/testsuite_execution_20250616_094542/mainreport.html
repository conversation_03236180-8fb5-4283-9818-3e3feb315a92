<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 6/16/2025, 9:50:19 AM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-passed">passed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">0</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 16/06/2025, 09:50:18
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="45 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #1 Test Case
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="rkL0oz4kiL.png" data-action-id="rkL0oz4kiL" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: nz.com.kmart <span class="action-id-badge" title="Action ID: rkL0oz4kiL">rkL0oz4kiL</span>
                            </div>
                            <span class="test-step-duration">3516ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="accessibil.png" data-action-id="accessibil" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with accessibility_id: txtHomeAccountCtaSignIn <span class="action-id-badge" title="Action ID: accessibil">accessibil</span>
                            </div>
                            <span class="test-step-duration">4089ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="sc2KH9bG6H.png" data-action-id="sc2KH9bG6H" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: alert_accept <span class="action-id-badge" title="Action ID: sc2KH9bG6H">sc2KH9bG6H</span>
                            </div>
                            <span class="test-step-duration">1227ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeTextField[@name="Email"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1536ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="pr9o8Zsm5p.png" data-action-id="pr9o8Zsm5p" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Execute Test Case: Kmart-NZ-Signin (8 steps) <span class="action-id-badge" title="Action ID: pr9o8Zsm5p">pr9o8Zsm5p</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="5gCo2OJCr6.png" data-action-id="5gCo2OJCr6" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Find" <span class="action-id-badge" title="Action ID: 5gCo2OJCr6">5gCo2OJCr6</span>
                            </div>
                            <span class="test-step-duration">4749ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="passed"
                            data-screenshot="y4i304JeJj.png" data-action-id="y4i304JeJj" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: y4i304JeJj">y4i304JeJj</span>
                            </div>
                            <span class="test-step-duration">2398ms</span>
                        </li>
                        <li class="test-step" data-step-id="8" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-7')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1460ms</span>
                        </li>
                        <li class="test-step" data-step-id="9" data-status="passed"
                            data-screenshot="alaozIePOy.png" data-action-id="alaozIePOy" onclick="showStepDetails('step-0-8')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: alaozIePOy">alaozIePOy</span>
                            </div>
                            <span class="test-step-duration">2470ms</span>
                        </li>
                        <li class="test-step" data-step-id="10" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-9')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1442ms</span>
                        </li>
                        <li class="test-step" data-step-id="11" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-10')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">12415ms</span>
                        </li>
                        <li class="test-step" data-step-id="12" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-11')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1686ms</span>
                        </li>
                        <li class="test-step" data-step-id="13" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-12')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2126ms</span>
                        </li>
                        <li class="test-step" data-step-id="14" data-status="passed"
                            data-screenshot="deviceback.png" data-action-id="deviceback" onclick="showStepDetails('step-0-13')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: deviceback-se.png <span class="action-id-badge" title="Action ID: deviceback">deviceback</span>
                            </div>
                            <span class="test-step-duration">2540ms</span>
                        </li>
                        <li class="test-step" data-step-id="15" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-14')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1617ms</span>
                        </li>
                        <li class="test-step" data-step-id="16" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-15')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1598ms</span>
                        </li>
                        <li class="test-step" data-step-id="17" data-status="passed"
                            data-screenshot="0q79ZemF4g.png" data-action-id="0q79ZemF4g" onclick="showStepDetails('step-0-16')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                iOS Function: text <span class="action-id-badge" title="Action ID: 0q79ZemF4g">0q79ZemF4g</span>
                            </div>
                            <span class="test-step-duration">2054ms</span>
                        </li>
                        <li class="test-step" data-step-id="18" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-17')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Filter"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1422ms</span>
                        </li>
                        <li class="test-step" data-step-id="19" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-18')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1437ms</span>
                        </li>
                        <li class="test-step" data-step-id="20" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-19')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1878ms</span>
                        </li>
                        <li class="test-step" data-step-id="21" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-20')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1702ms</span>
                        </li>
                        <li class="test-step" data-step-id="22" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-21')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2146ms</span>
                        </li>
                        <li class="test-step" data-step-id="23" data-status="passed"
                            data-screenshot="deviceback.png" data-action-id="deviceback" onclick="showStepDetails('step-0-22')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: deviceback-se.png <span class="action-id-badge" title="Action ID: deviceback">deviceback</span>
                            </div>
                            <span class="test-step-duration">2594ms</span>
                        </li>
                        <li class="test-step" data-step-id="24" data-status="passed"
                            data-screenshot="3caMBvQX7k.png" data-action-id="3caMBvQX7k" onclick="showStepDetails('step-0-23')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Swipe from (50%, 70%) to (50%, 30%) <span class="action-id-badge" title="Action ID: 3caMBvQX7k">3caMBvQX7k</span>
                            </div>
                            <span class="test-step-duration">2352ms</span>
                        </li>
                        <li class="test-step" data-step-id="25" data-status="failed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-24')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">3154ms</span>
                        </li>
                        <li class="test-step" data-step-id="26" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-25')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">8847ms</span>
                        </li>
                        <li class="test-step" data-step-id="27" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-26')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2103ms</span>
                        </li>
                        <li class="test-step" data-step-id="28" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-27')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1092ms</span>
                        </li>
                        <li class="test-step" data-step-id="29" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-28')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1577ms</span>
                        </li>
                        <li class="test-step" data-step-id="30" data-status="passed"
                            data-screenshot="ZCdhKRqSd3.png" data-action-id="ZCdhKRqSd3" onclick="showStepDetails('step-0-29')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Move" <span class="action-id-badge" title="Action ID: ZCdhKRqSd3">ZCdhKRqSd3</span>
                            </div>
                            <span class="test-step-duration">2621ms</span>
                        </li>
                        <li class="test-step" data-step-id="31" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-30')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1588ms</span>
                        </li>
                        <li class="test-step" data-step-id="32" data-status="passed"
                            data-screenshot="uOt2cFGhGr.png" data-action-id="uOt2cFGhGr" onclick="showStepDetails('step-0-31')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: uOt2cFGhGr">uOt2cFGhGr</span>
                            </div>
                            <span class="test-step-duration">2612ms</span>
                        </li>
                        <li class="test-step" data-step-id="33" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-32')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1592ms</span>
                        </li>
                        <li class="test-step" data-step-id="34" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-33')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1696ms</span>
                        </li>
                        <li class="test-step" data-step-id="35" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-34')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2108ms</span>
                        </li>
                        <li class="test-step" data-step-id="36" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-35')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1408ms</span>
                        </li>
                        <li class="test-step" data-step-id="37" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-36')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">12598ms</span>
                        </li>
                        <li class="test-step" data-step-id="38" data-status="passed"
                            data-screenshot="yhmzeynQyu.png" data-action-id="yhmzeynQyu" onclick="showStepDetails('step-0-37')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on image: banner-close-updated.png <span class="action-id-badge" title="Action ID: yhmzeynQyu">yhmzeynQyu</span>
                            </div>
                            <span class="test-step-duration">2190ms</span>
                        </li>
                        <li class="test-step" data-step-id="39" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-38')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2111ms</span>
                        </li>
                        <li class="test-step" data-step-id="40" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-39')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2303ms</span>
                        </li>
                        <li class="test-step" data-step-id="41" data-status="passed"
                            data-screenshot="yUJyVO5Wev.png" data-action-id="yUJyVO5Wev" onclick="showStepDetails('step-0-40')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: yUJyVO5Wev">yUJyVO5Wev</span>
                            </div>
                            <span class="test-step-duration">2575ms</span>
                        </li>
                        <li class="test-step" data-step-id="42" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-41')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">2268ms</span>
                        </li>
                        <li class="test-step" data-step-id="43" data-status="passed"
                            data-screenshot="PH8FFnzohm.png" data-action-id="PH8FFnzohm" onclick="showStepDetails('step-0-42')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Remove" <span class="action-id-badge" title="Action ID: PH8FFnzohm">PH8FFnzohm</span>
                            </div>
                            <span class="test-step-duration">2546ms</span>
                        </li>
                        <li class="test-step" data-step-id="44" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-43')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">1587ms</span>
                        </li>
                        <li class="test-step" data-step-id="45" data-status="passed"
                            data-screenshot="XCUIElemen.png" data-action-id="XCUIElemen" onclick="showStepDetails('step-0-44')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"] <span class="action-id-badge" title="Action ID: XCUIElemen">XCUIElemen</span>
                            </div>
                            <span class="test-step-duration">10586ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 16/06/2025, 09:50:18","testCases":[{"name":"Test Case","status":"passed","steps":[{"name":"Restart app: nz.com.kmart","status":"passed","duration":"3516ms","action_id":"rkL0oz4kiL","screenshot_filename":"rkL0oz4kiL.png","report_screenshot":"rkL0oz4kiL.png","resolved_screenshot":"screenshots/rkL0oz4kiL.png","clean_action_id":"rkL0oz4kiL","prefixed_action_id":"al_rkL0oz4kiL","action_id_screenshot":"screenshots/rkL0oz4kiL.png"},{"name":"Tap on element with accessibility_id: txtHomeAccountCtaSignIn","status":"passed","duration":"4089ms","action_id":"accessibil","screenshot_filename":"accessibil.png","report_screenshot":"accessibil.png","resolved_screenshot":"screenshots/accessibil.png","action_id_screenshot":"screenshots/accessibil.png"},{"name":"iOS Function: alert_accept","status":"passed","duration":"1227ms","action_id":"sc2KH9bG6H","screenshot_filename":"sc2KH9bG6H.png","report_screenshot":"sc2KH9bG6H.png","resolved_screenshot":"screenshots/sc2KH9bG6H.png","clean_action_id":"sc2KH9bG6H","prefixed_action_id":"al_sc2KH9bG6H","action_id_screenshot":"screenshots/sc2KH9bG6H.png"},{"name":"Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]","status":"passed","duration":"1536ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Execute Test Case: Kmart-NZ-Signin (8 steps)","status":"passed","duration":"0ms","action_id":"pr9o8Zsm5p","screenshot_filename":"pr9o8Zsm5p.png","report_screenshot":"pr9o8Zsm5p.png","resolved_screenshot":"screenshots/pr9o8Zsm5p.png","clean_action_id":"pr9o8Zsm5p","prefixed_action_id":"al_pr9o8Zsm5p","action_id_screenshot":"screenshots/pr9o8Zsm5p.png"},{"name":"Tap on Text: \"Find\"","status":"passed","duration":"4749ms","action_id":"5gCo2OJCr6","screenshot_filename":"5gCo2OJCr6.png","report_screenshot":"5gCo2OJCr6.png","resolved_screenshot":"screenshots/5gCo2OJCr6.png","clean_action_id":"5gCo2OJCr6","prefixed_action_id":"al_5gCo2OJCr6","action_id_screenshot":"screenshots/5gCo2OJCr6.png"},{"name":"iOS Function: text","status":"passed","duration":"2398ms","action_id":"y4i304JeJj","screenshot_filename":"y4i304JeJj.png","report_screenshot":"y4i304JeJj.png","resolved_screenshot":"screenshots/y4i304JeJj.png","clean_action_id":"y4i304JeJj","prefixed_action_id":"al_y4i304JeJj","action_id_screenshot":"screenshots/y4i304JeJj.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1460ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2470ms","action_id":"alaozIePOy","screenshot_filename":"alaozIePOy.png","report_screenshot":"alaozIePOy.png","resolved_screenshot":"screenshots/alaozIePOy.png","clean_action_id":"alaozIePOy","prefixed_action_id":"al_alaozIePOy","action_id_screenshot":"screenshots/alaozIePOy.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1442ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"12415ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1686ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2126ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: deviceback-se.png","status":"passed","duration":"2540ms","action_id":"deviceback","screenshot_filename":"deviceback.png","report_screenshot":"deviceback.png","resolved_screenshot":"screenshots/deviceback.png","action_id_screenshot":"screenshots/deviceback.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]","status":"passed","duration":"1617ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]","status":"passed","duration":"1598ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"iOS Function: text","status":"passed","duration":"2054ms","action_id":"0q79ZemF4g","screenshot_filename":"0q79ZemF4g.png","report_screenshot":"0q79ZemF4g.png","resolved_screenshot":"screenshots/0q79ZemF4g.png","clean_action_id":"0q79ZemF4g","prefixed_action_id":"al_0q79ZemF4g","action_id_screenshot":"screenshots/0q79ZemF4g.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]","status":"passed","duration":"1422ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]","status":"passed","duration":"1437ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]","status":"passed","duration":"1878ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1702ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"2146ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: deviceback-se.png","status":"passed","duration":"2594ms","action_id":"deviceback","screenshot_filename":"deviceback.png","report_screenshot":"deviceback.png","resolved_screenshot":"screenshots/deviceback.png","action_id_screenshot":"screenshots/deviceback.png"},{"name":"Swipe from (50%, 70%) to (50%, 30%)","status":"passed","duration":"2352ms","action_id":"3caMBvQX7k","screenshot_filename":"3caMBvQX7k.png","report_screenshot":"3caMBvQX7k.png","resolved_screenshot":"screenshots/3caMBvQX7k.png","clean_action_id":"3caMBvQX7k","prefixed_action_id":"al_3caMBvQX7k","action_id_screenshot":"screenshots/3caMBvQX7k.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]","status":"failed","duration":"3154ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]","status":"passed","duration":"8847ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2103ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"1092ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"1577ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Move\"","status":"passed","duration":"2621ms","action_id":"ZCdhKRqSd3","screenshot_filename":"ZCdhKRqSd3.png","report_screenshot":"ZCdhKRqSd3.png","resolved_screenshot":"screenshots/ZCdhKRqSd3.png","clean_action_id":"ZCdhKRqSd3","prefixed_action_id":"al_ZCdhKRqSd3","action_id_screenshot":"screenshots/ZCdhKRqSd3.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"1588ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"2612ms","action_id":"uOt2cFGhGr","screenshot_filename":"uOt2cFGhGr.png","report_screenshot":"uOt2cFGhGr.png","resolved_screenshot":"screenshots/uOt2cFGhGr.png","clean_action_id":"uOt2cFGhGr","prefixed_action_id":"al_uOt2cFGhGr","action_id_screenshot":"screenshots/uOt2cFGhGr.png"},{"name":"Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]","status":"passed","duration":"1592ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]","status":"passed","duration":"1696ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]","status":"passed","duration":"2108ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"passed","duration":"1408ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]","status":"passed","duration":"12598ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on image: banner-close-updated.png","status":"passed","duration":"2190ms","action_id":"yhmzeynQyu","screenshot_filename":"yhmzeynQyu.png","report_screenshot":"yhmzeynQyu.png","resolved_screenshot":"screenshots/yhmzeynQyu.png","clean_action_id":"yhmzeynQyu","prefixed_action_id":"al_yhmzeynQyu","action_id_screenshot":"screenshots/yhmzeynQyu.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]","status":"passed","duration":"2111ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"2303ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"2575ms","action_id":"yUJyVO5Wev","screenshot_filename":"yUJyVO5Wev.png","report_screenshot":"yUJyVO5Wev.png","resolved_screenshot":"screenshots/yUJyVO5Wev.png","clean_action_id":"yUJyVO5Wev","prefixed_action_id":"al_yUJyVO5Wev","action_id_screenshot":"screenshots/yUJyVO5Wev.png"},{"name":"If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]","status":"passed","duration":"2268ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on Text: \"Remove\"","status":"passed","duration":"2546ms","action_id":"PH8FFnzohm","screenshot_filename":"PH8FFnzohm.png","report_screenshot":"PH8FFnzohm.png","resolved_screenshot":"screenshots/PH8FFnzohm.png","clean_action_id":"PH8FFnzohm","prefixed_action_id":"al_PH8FFnzohm","action_id_screenshot":"screenshots/PH8FFnzohm.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]","status":"passed","duration":"1587ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"},{"name":"Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]","status":"passed","duration":"10586ms","action_id":"XCUIElemen","screenshot_filename":"XCUIElemen.png","report_screenshot":"XCUIElemen.png","resolved_screenshot":"screenshots/XCUIElemen.png","action_id_screenshot":"screenshots/XCUIElemen.png"}]}],"passed":1,"failed":0,"skipped":0,"status":"passed","availableScreenshots":["0q79ZemF4g.png","3caMBvQX7k.png","5gCo2OJCr6.png","8umPSX0vrr.png","BzTvnSrykE.png","EELcfo48Sh.png","F1olhgKhUt.png","HotUJOd6oB.png","ITHvSyXXmu.png","K7yV3GGsgr.png","OyUowAaBzD.png","PH8FFnzohm.png","Q0fomJIDoQ.png","Qbg9bipTGs.png","RCYxT9YD8u.png","WbxRVpWtjw.png","Xzuxm4XngK.png","ZCdhKRqSd3.png","alaozIePOy.png","eLxHVWKeDQ.png","fzahGiveKf.png","jIeR7BPEPu.png","k3mu9Mt7Ec.png","lWIRxRm6HE.png","nAB6Q8LAdv.png","oWLIFhrzr1.png","pr9o8Zsm5p.png","qPv5C4K0a2.png","rkL0oz4kiL.png","rqLJpAP0mA.png","sc2KH9bG6H.png","tH68tAo0l0.png","uOt2cFGhGr.png","y4i304JeJj.png","yUJyVO5Wev.png","yhmzeynQyu.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>
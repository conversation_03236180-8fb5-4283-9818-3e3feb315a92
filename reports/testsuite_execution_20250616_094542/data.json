{"name": "UI Execution 16/06/2025, 09:50:18", "testCases": [{"name": "Test Case", "status": "passed", "steps": [{"name": "Restart app: nz.com.kmart", "status": "passed", "duration": "3516ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4089ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1227ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1536ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-NZ-Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "pr9o8Zsm5p", "screenshot_filename": "pr9o8Zsm5p.png", "report_screenshot": "pr9o8Zsm5p.png", "resolved_screenshot": "screenshots/pr9o8Zsm5p.png", "clean_action_id": "pr9o8Zsm5p", "prefixed_action_id": "al_pr9o8Zsm5p", "action_id_screenshot": "screenshots/pr9o8Zsm5p.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4749ms", "action_id": "5gCo2OJCr6", "screenshot_filename": "5gCo2OJCr6.png", "report_screenshot": "5gCo2OJCr6.png", "resolved_screenshot": "screenshots/5gCo2OJCr6.png", "clean_action_id": "5gCo2OJCr6", "prefixed_action_id": "al_5gCo2OJCr6", "action_id_screenshot": "screenshots/5gCo2OJCr6.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2398ms", "action_id": "y4i304JeJj", "screenshot_filename": "y4i304JeJj.png", "report_screenshot": "y4i304JeJj.png", "resolved_screenshot": "screenshots/y4i304JeJj.png", "clean_action_id": "y4i304JeJj", "prefixed_action_id": "al_y4i304JeJj", "action_id_screenshot": "screenshots/y4i304JeJj.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1460ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2470ms", "action_id": "alaozIePOy", "screenshot_filename": "alaozIePOy.png", "report_screenshot": "alaozIePOy.png", "resolved_screenshot": "screenshots/alaozIePOy.png", "clean_action_id": "alaozIePOy", "prefixed_action_id": "al_alaozIePOy", "action_id_screenshot": "screenshots/alaozIePOy.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1442ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "12415ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1686ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2126ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: deviceback-se.png", "status": "passed", "duration": "2540ms", "action_id": "deviceback", "screenshot_filename": "deviceback.png", "report_screenshot": "deviceback.png", "resolved_screenshot": "screenshots/deviceback.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "status": "passed", "duration": "1617ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"imgBtnSearch\"]", "status": "passed", "duration": "1598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2054ms", "action_id": "0q79ZemF4g", "screenshot_filename": "0q79ZemF4g.png", "report_screenshot": "0q79ZemF4g.png", "resolved_screenshot": "screenshots/0q79ZemF4g.png", "clean_action_id": "0q79ZemF4g", "prefixed_action_id": "al_0q79ZemF4g", "action_id_screenshot": "screenshots/0q79ZemF4g.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1422ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1437ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1878ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1702ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2146ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: deviceback-se.png", "status": "passed", "duration": "2594ms", "action_id": "deviceback", "screenshot_filename": "deviceback.png", "report_screenshot": "deviceback.png", "resolved_screenshot": "screenshots/deviceback.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2352ms", "action_id": "3caMBvQX7k", "screenshot_filename": "3caMBvQX7k.png", "report_screenshot": "3caMBvQX7k.png", "resolved_screenshot": "screenshots/3caMBvQX7k.png", "clean_action_id": "3caMBvQX7k", "prefixed_action_id": "al_3caMBvQX7k", "action_id_screenshot": "screenshots/3caMBvQX7k.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "status": "failed", "duration": "3154ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "8847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2103ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1092ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1577ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "2621ms", "action_id": "ZCdhKRqSd3", "screenshot_filename": "ZCdhKRqSd3.png", "report_screenshot": "ZCdhKRqSd3.png", "resolved_screenshot": "screenshots/ZCdhKRqSd3.png", "clean_action_id": "ZCdhKRqSd3", "prefixed_action_id": "al_ZCdhKRqSd3", "action_id_screenshot": "screenshots/ZCdhKRqSd3.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "1588ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2612ms", "action_id": "uOt2cFGhGr", "screenshot_filename": "uOt2cFGhGr.png", "report_screenshot": "uOt2cFGhGr.png", "resolved_screenshot": "screenshots/uOt2cFGhGr.png", "clean_action_id": "uOt2cFGhGr", "prefixed_action_id": "al_uOt2cFGhGr", "action_id_screenshot": "screenshots/uOt2cFGhGr.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "passed", "duration": "1592ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1696ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "passed", "duration": "2108ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "1408ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "passed", "duration": "12598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "2190ms", "action_id": "<PERSON><PERSON><PERSON>ynQyu", "screenshot_filename": "yhmzeynQyu.png", "report_screenshot": "yhmzeynQyu.png", "resolved_screenshot": "screenshots/yhmzeynQyu.png", "clean_action_id": "<PERSON><PERSON><PERSON>ynQyu", "prefixed_action_id": "al_yhm<PERSON>ynQyu", "action_id_screenshot": "screenshots/yhmzeynQyu.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "passed", "duration": "2111ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2303ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2575ms", "action_id": "yUJyVO5Wev", "screenshot_filename": "yUJyVO5Wev.png", "report_screenshot": "yUJyVO5Wev.png", "resolved_screenshot": "screenshots/yUJyVO5Wev.png", "clean_action_id": "yUJyVO5Wev", "prefixed_action_id": "al_yUJyVO5Wev", "action_id_screenshot": "screenshots/yUJyVO5Wev.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "passed", "duration": "2268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2546ms", "action_id": "PH8FFnzohm", "screenshot_filename": "PH8FFnzohm.png", "report_screenshot": "PH8FFnzohm.png", "resolved_screenshot": "screenshots/PH8FFnzohm.png", "clean_action_id": "PH8FFnzohm", "prefixed_action_id": "al_PH8FFnzohm", "action_id_screenshot": "screenshots/PH8FFnzohm.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "passed", "duration": "1587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "passed", "duration": "10586ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}], "passed": 1, "failed": 0, "skipped": 0, "status": "passed"}
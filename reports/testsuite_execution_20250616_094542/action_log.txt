Action Log - 2025-06-16 09:50:19
================================================================================

[[09:50:18]] [INFO] Generating execution report...
[[09:50:18]] [SUCCESS] All tests passed successfully!
[[09:50:18]] [SUCCESS] Screenshot refreshed
[[09:50:18]] [INFO] Refreshing screenshot...
[[09:50:05]] [SUCCESS] Screenshot refreshed successfully
[[09:50:05]] [SUCCESS] Screenshot refreshed successfully
[[09:50:05]] [INFO] Executing action 45/45: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[09:50:05]] [SUCCESS] Screenshot refreshed
[[09:50:05]] [INFO] Refreshing screenshot...
[[09:50:02]] [SUCCESS] Screenshot refreshed successfully
[[09:50:02]] [SUCCESS] Screenshot refreshed successfully
[[09:50:02]] [INFO] Executing action 44/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[09:50:01]] [SUCCESS] Screenshot refreshed
[[09:50:01]] [INFO] Refreshing screenshot...
[[09:49:57]] [SUCCESS] Screenshot refreshed successfully
[[09:49:57]] [SUCCESS] Screenshot refreshed successfully
[[09:49:57]] [INFO] Executing action 43/45: Tap on Text: "Remove"
[[09:49:56]] [SUCCESS] Screenshot refreshed
[[09:49:56]] [INFO] Refreshing screenshot...
[[09:49:53]] [SUCCESS] Screenshot refreshed successfully
[[09:49:53]] [SUCCESS] Screenshot refreshed successfully
[[09:49:53]] [INFO] Executing action 42/45: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[09:49:52]] [SUCCESS] Screenshot refreshed
[[09:49:52]] [INFO] Refreshing screenshot...
[[09:49:48]] [SUCCESS] Screenshot refreshed successfully
[[09:49:48]] [SUCCESS] Screenshot refreshed successfully
[[09:49:48]] [INFO] Executing action 41/45: Tap on Text: "Remove"
[[09:49:47]] [SUCCESS] Screenshot refreshed
[[09:49:47]] [INFO] Refreshing screenshot...
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:44]] [SUCCESS] Screenshot refreshed successfully
[[09:49:43]] [INFO] Executing action 40/45: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[09:49:43]] [SUCCESS] Screenshot refreshed
[[09:49:43]] [INFO] Refreshing screenshot...
[[09:49:40]] [SUCCESS] Screenshot refreshed successfully
[[09:49:40]] [SUCCESS] Screenshot refreshed successfully
[[09:49:39]] [INFO] Executing action 39/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[09:49:39]] [SUCCESS] Screenshot refreshed
[[09:49:39]] [INFO] Refreshing screenshot...
[[09:49:34]] [INFO] Executing action 38/45: Tap on image: banner-close-updated.png
[[09:49:34]] [SUCCESS] Screenshot refreshed successfully
[[09:49:34]] [SUCCESS] Screenshot refreshed successfully
[[09:49:34]] [SUCCESS] Screenshot refreshed
[[09:49:34]] [INFO] Refreshing screenshot...
[[09:49:20]] [SUCCESS] Screenshot refreshed successfully
[[09:49:20]] [SUCCESS] Screenshot refreshed successfully
[[09:49:20]] [INFO] Executing action 37/45: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[09:49:19]] [SUCCESS] Screenshot refreshed
[[09:49:19]] [INFO] Refreshing screenshot...
[[09:49:16]] [INFO] Executing action 36/45: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[09:49:16]] [SUCCESS] Screenshot refreshed successfully
[[09:49:16]] [SUCCESS] Screenshot refreshed successfully
[[09:49:16]] [SUCCESS] Screenshot refreshed
[[09:49:16]] [INFO] Refreshing screenshot...
[[09:49:13]] [SUCCESS] Screenshot refreshed successfully
[[09:49:13]] [SUCCESS] Screenshot refreshed successfully
[[09:49:12]] [INFO] Executing action 35/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[09:49:12]] [SUCCESS] Screenshot refreshed
[[09:49:12]] [INFO] Refreshing screenshot...
[[09:49:08]] [SUCCESS] Screenshot refreshed successfully
[[09:49:08]] [SUCCESS] Screenshot refreshed successfully
[[09:49:08]] [INFO] Executing action 34/45: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:49:07]] [SUCCESS] Screenshot refreshed
[[09:49:07]] [INFO] Refreshing screenshot...
[[09:49:04]] [SUCCESS] Screenshot refreshed successfully
[[09:49:04]] [SUCCESS] Screenshot refreshed successfully
[[09:49:04]] [INFO] Executing action 33/45: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[09:49:04]] [SUCCESS] Screenshot refreshed
[[09:49:04]] [INFO] Refreshing screenshot...
[[09:48:59]] [SUCCESS] Screenshot refreshed successfully
[[09:48:59]] [SUCCESS] Screenshot refreshed successfully
[[09:48:59]] [INFO] Executing action 32/45: Tap on Text: "Remove"
[[09:48:59]] [SUCCESS] Screenshot refreshed
[[09:48:59]] [INFO] Refreshing screenshot...
[[09:48:56]] [SUCCESS] Screenshot refreshed successfully
[[09:48:56]] [SUCCESS] Screenshot refreshed successfully
[[09:48:56]] [INFO] Executing action 31/45: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[09:48:55]] [SUCCESS] Screenshot refreshed
[[09:48:55]] [INFO] Refreshing screenshot...
[[09:48:51]] [SUCCESS] Screenshot refreshed successfully
[[09:48:51]] [SUCCESS] Screenshot refreshed successfully
[[09:48:51]] [INFO] Executing action 30/45: Tap on Text: "Move"
[[09:48:50]] [SUCCESS] Screenshot refreshed
[[09:48:50]] [INFO] Refreshing screenshot...
[[09:48:47]] [SUCCESS] Screenshot refreshed successfully
[[09:48:47]] [SUCCESS] Screenshot refreshed successfully
[[09:48:47]] [INFO] Executing action 29/45: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[09:48:46]] [SUCCESS] Screenshot refreshed
[[09:48:46]] [INFO] Refreshing screenshot...
[[09:48:44]] [SUCCESS] Screenshot refreshed successfully
[[09:48:44]] [SUCCESS] Screenshot refreshed successfully
[[09:48:44]] [INFO] Executing action 28/45: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[09:48:43]] [SUCCESS] Screenshot refreshed
[[09:48:43]] [INFO] Refreshing screenshot...
[[09:48:40]] [SUCCESS] Screenshot refreshed successfully
[[09:48:40]] [SUCCESS] Screenshot refreshed successfully
[[09:48:39]] [INFO] Executing action 27/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[09:48:39]] [SUCCESS] Screenshot refreshed
[[09:48:39]] [INFO] Refreshing screenshot...
[[09:48:28]] [INFO] Executing action 26/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:48:28]] [ERROR] Action 25 failed: Element not found or not tappable: xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]'
[[09:48:14]] [SUCCESS] Screenshot refreshed successfully
[[09:48:14]] [SUCCESS] Screenshot refreshed successfully
[[09:48:14]] [INFO] Executing action 25/45: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[09:48:13]] [SUCCESS] Screenshot refreshed
[[09:48:13]] [INFO] Refreshing screenshot...
[[09:48:09]] [SUCCESS] Screenshot refreshed successfully
[[09:48:09]] [SUCCESS] Screenshot refreshed successfully
[[09:48:09]] [INFO] Executing action 24/45: Swipe from (50%, 70%) to (50%, 30%)
[[09:48:08]] [SUCCESS] Screenshot refreshed
[[09:48:08]] [INFO] Refreshing screenshot...
[[09:48:04]] [SUCCESS] Screenshot refreshed successfully
[[09:48:04]] [SUCCESS] Screenshot refreshed successfully
[[09:48:03]] [INFO] Executing action 23/45: Tap on image: deviceback-se.png
[[09:48:03]] [SUCCESS] Screenshot refreshed
[[09:48:03]] [INFO] Refreshing screenshot...
[[09:47:59]] [SUCCESS] Screenshot refreshed successfully
[[09:47:59]] [SUCCESS] Screenshot refreshed successfully
[[09:47:59]] [INFO] Executing action 22/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:47:58]] [SUCCESS] Screenshot refreshed
[[09:47:58]] [INFO] Refreshing screenshot...
[[09:47:55]] [SUCCESS] Screenshot refreshed successfully
[[09:47:55]] [SUCCESS] Screenshot refreshed successfully
[[09:47:54]] [INFO] Executing action 21/45: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:47:54]] [SUCCESS] Screenshot refreshed
[[09:47:54]] [INFO] Refreshing screenshot...
[[09:47:50]] [SUCCESS] Screenshot refreshed successfully
[[09:47:50]] [SUCCESS] Screenshot refreshed successfully
[[09:47:50]] [INFO] Executing action 20/45: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]
[[09:47:49]] [SUCCESS] Screenshot refreshed
[[09:47:49]] [INFO] Refreshing screenshot...
[[09:47:46]] [SUCCESS] Screenshot refreshed successfully
[[09:47:46]] [SUCCESS] Screenshot refreshed successfully
[[09:47:46]] [INFO] Executing action 19/45: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:47:45]] [SUCCESS] Screenshot refreshed
[[09:47:45]] [INFO] Refreshing screenshot...
[[09:47:43]] [SUCCESS] Screenshot refreshed successfully
[[09:47:43]] [SUCCESS] Screenshot refreshed successfully
[[09:47:42]] [INFO] Executing action 18/45: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:47:42]] [SUCCESS] Screenshot refreshed
[[09:47:42]] [INFO] Refreshing screenshot...
[[09:47:38]] [SUCCESS] Screenshot refreshed successfully
[[09:47:38]] [SUCCESS] Screenshot refreshed successfully
[[09:47:37]] [INFO] Executing action 17/45: iOS Function: text
[[09:47:37]] [SUCCESS] Screenshot refreshed
[[09:47:37]] [INFO] Refreshing screenshot...
[[09:47:34]] [SUCCESS] Screenshot refreshed successfully
[[09:47:34]] [SUCCESS] Screenshot refreshed successfully
[[09:47:34]] [INFO] Executing action 16/45: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[09:47:33]] [SUCCESS] Screenshot refreshed
[[09:47:33]] [INFO] Refreshing screenshot...
[[09:47:30]] [SUCCESS] Screenshot refreshed successfully
[[09:47:30]] [SUCCESS] Screenshot refreshed successfully
[[09:47:30]] [INFO] Executing action 15/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[09:47:29]] [SUCCESS] Screenshot refreshed
[[09:47:29]] [INFO] Refreshing screenshot...
[[09:47:26]] [SUCCESS] Screenshot refreshed successfully
[[09:47:26]] [SUCCESS] Screenshot refreshed successfully
[[09:47:25]] [INFO] Executing action 14/45: Tap on image: deviceback-se.png
[[09:47:25]] [SUCCESS] Screenshot refreshed
[[09:47:25]] [INFO] Refreshing screenshot...
[[09:47:21]] [SUCCESS] Screenshot refreshed successfully
[[09:47:21]] [SUCCESS] Screenshot refreshed successfully
[[09:47:21]] [INFO] Executing action 13/45: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:47:20]] [SUCCESS] Screenshot refreshed
[[09:47:20]] [INFO] Refreshing screenshot...
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:17]] [SUCCESS] Screenshot refreshed successfully
[[09:47:17]] [INFO] Executing action 12/45: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:47:16]] [SUCCESS] Screenshot refreshed
[[09:47:16]] [INFO] Refreshing screenshot...
[[09:47:02]] [SUCCESS] Screenshot refreshed successfully
[[09:47:02]] [SUCCESS] Screenshot refreshed successfully
[[09:47:02]] [INFO] Executing action 11/45: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:47:01]] [SUCCESS] Screenshot refreshed
[[09:47:01]] [INFO] Refreshing screenshot...
[[09:46:58]] [SUCCESS] Screenshot refreshed successfully
[[09:46:58]] [SUCCESS] Screenshot refreshed successfully
[[09:46:58]] [INFO] Executing action 10/45: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:46:57]] [SUCCESS] Screenshot refreshed
[[09:46:57]] [INFO] Refreshing screenshot...
[[09:46:53]] [SUCCESS] Screenshot refreshed successfully
[[09:46:53]] [SUCCESS] Screenshot refreshed successfully
[[09:46:53]] [INFO] Executing action 9/45: Swipe from (50%, 70%) to (50%, 30%)
[[09:46:52]] [SUCCESS] Screenshot refreshed
[[09:46:52]] [INFO] Refreshing screenshot...
[[09:46:49]] [SUCCESS] Screenshot refreshed successfully
[[09:46:49]] [SUCCESS] Screenshot refreshed successfully
[[09:46:49]] [INFO] Executing action 8/45: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:46:48]] [SUCCESS] Screenshot refreshed
[[09:46:48]] [INFO] Refreshing screenshot...
[[09:46:44]] [SUCCESS] Screenshot refreshed successfully
[[09:46:44]] [SUCCESS] Screenshot refreshed successfully
[[09:46:44]] [INFO] Executing action 7/45: iOS Function: text
[[09:46:44]] [SUCCESS] Screenshot refreshed
[[09:46:44]] [INFO] Refreshing screenshot...
[[09:46:39]] [SUCCESS] Screenshot refreshed successfully
[[09:46:39]] [SUCCESS] Screenshot refreshed successfully
[[09:46:37]] [INFO] Executing action 6/45: Tap on Text: "Find"
[[09:46:37]] [SUCCESS] Screenshot refreshed
[[09:46:37]] [INFO] Refreshing screenshot...
[[09:46:37]] [SUCCESS] Screenshot refreshed
[[09:46:37]] [INFO] Refreshing screenshot...
[[09:46:34]] [SUCCESS] Screenshot refreshed successfully
[[09:46:34]] [SUCCESS] Screenshot refreshed successfully
[[09:46:33]] [INFO] Executing Multi Step action step 8/8: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[09:46:33]] [SUCCESS] Screenshot refreshed
[[09:46:33]] [INFO] Refreshing screenshot...
[[09:46:28]] [SUCCESS] Screenshot refreshed successfully
[[09:46:28]] [SUCCESS] Screenshot refreshed successfully
[[09:46:28]] [INFO] Executing Multi Step action step 7/8: iOS Function: text
[[09:46:28]] [SUCCESS] Screenshot refreshed
[[09:46:28]] [INFO] Refreshing screenshot...
[[09:46:24]] [SUCCESS] Screenshot refreshed successfully
[[09:46:24]] [SUCCESS] Screenshot refreshed successfully
[[09:46:24]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:46:24]] [SUCCESS] Screenshot refreshed
[[09:46:24]] [INFO] Refreshing screenshot...
[[09:46:19]] [SUCCESS] Screenshot refreshed successfully
[[09:46:19]] [SUCCESS] Screenshot refreshed successfully
[[09:46:19]] [INFO] Executing Multi Step action step 5/8: Tap on Text: "Continue"
[[09:46:18]] [SUCCESS] Screenshot refreshed
[[09:46:18]] [INFO] Refreshing screenshot...
[[09:46:14]] [SUCCESS] Screenshot refreshed successfully
[[09:46:14]] [SUCCESS] Screenshot refreshed successfully
[[09:46:14]] [INFO] Executing Multi Step action step 4/8: Tap on image: keyboard_done_iphoneSE.png
[[09:46:14]] [SUCCESS] Screenshot refreshed
[[09:46:14]] [INFO] Refreshing screenshot...
[[09:46:11]] [SUCCESS] Screenshot refreshed successfully
[[09:46:11]] [SUCCESS] Screenshot refreshed successfully
[[09:46:11]] [INFO] Executing Multi Step action step 3/8: Input text: "<EMAIL>"
[[09:46:10]] [SUCCESS] Screenshot refreshed
[[09:46:10]] [INFO] Refreshing screenshot...
[[09:46:07]] [SUCCESS] Screenshot refreshed successfully
[[09:46:07]] [SUCCESS] Screenshot refreshed successfully
[[09:46:06]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:46:06]] [SUCCESS] Screenshot refreshed
[[09:46:06]] [INFO] Refreshing screenshot...
[[09:46:03]] [SUCCESS] Screenshot refreshed successfully
[[09:46:03]] [SUCCESS] Screenshot refreshed successfully
[[09:46:03]] [INFO] Executing Multi Step action step 1/8: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:46:03]] [INFO] Loaded 8 steps from test case: Kmart-NZ-Signin
[[09:46:02]] [INFO] Loading steps for Multi Step action: Kmart-NZ-Signin
[[09:46:02]] [INFO] Executing action 5/45: Execute Test Case: Kmart-NZ-Signin (8 steps)
[[09:46:02]] [SUCCESS] Screenshot refreshed
[[09:46:02]] [INFO] Refreshing screenshot...
[[09:45:59]] [SUCCESS] Screenshot refreshed successfully
[[09:45:59]] [SUCCESS] Screenshot refreshed successfully
[[09:45:59]] [INFO] Executing action 4/45: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:45:58]] [SUCCESS] Screenshot refreshed
[[09:45:58]] [INFO] Refreshing screenshot...
[[09:45:56]] [SUCCESS] Screenshot refreshed successfully
[[09:45:56]] [SUCCESS] Screenshot refreshed successfully
[[09:45:56]] [INFO] Executing action 3/45: iOS Function: alert_accept
[[09:45:55]] [SUCCESS] Screenshot refreshed
[[09:45:55]] [INFO] Refreshing screenshot...
[[09:45:49]] [SUCCESS] Screenshot refreshed successfully
[[09:45:49]] [SUCCESS] Screenshot refreshed successfully
[[09:45:49]] [INFO] Executing action 2/45: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:45:48]] [SUCCESS] Screenshot refreshed
[[09:45:48]] [INFO] Refreshing screenshot...
[[09:45:42]] [INFO] Executing action 1/45: Restart app: nz.com.kmart
[[09:45:42]] [INFO] ExecutionManager: Starting execution of 45 actions...
[[09:45:42]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[09:45:42]] [INFO] Clearing screenshots from database before execution...
[[09:45:42]] [SUCCESS] All screenshots deleted successfully
[[09:45:42]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:45:42]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_094542/screenshots
[[09:45:42]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_094542
[[09:45:42]] [SUCCESS] Report directory initialized successfully
[[09:45:42]] [INFO] Initializing report directory and screenshots folder...
[[09:45:38]] [SUCCESS] All screenshots deleted successfully
[[09:45:38]] [SUCCESS] Loaded test case "WishList NZ" with 45 actions
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tapOnText
[[09:45:38]] [SUCCESS] Added action: ifElseSteps
[[09:45:38]] [SUCCESS] Added action: tapOnText
[[09:45:38]] [SUCCESS] Added action: ifElseSteps
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tapOnText
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tapOnText
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: swipe
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: iosFunctions
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: swipe
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: iosFunctions
[[09:45:38]] [SUCCESS] Added action: tapOnText
[[09:45:38]] [SUCCESS] Added action: multiStep
[[09:45:38]] [SUCCESS] Added action: waitTill
[[09:45:38]] [SUCCESS] Added action: iosFunctions
[[09:45:38]] [SUCCESS] Added action: tap
[[09:45:38]] [SUCCESS] Added action: restartApp
[[09:45:38]] [INFO] All actions cleared
[[09:45:38]] [INFO] Cleaning up screenshots...
[[09:45:37]] [SUCCESS] Screenshot refreshed successfully
[[09:45:35]] [SUCCESS] Screenshot refreshed
[[09:45:35]] [INFO] Refreshing screenshot...
[[09:45:34]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[09:45:34]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[09:45:28]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[09:45:26]] [SUCCESS] Found 1 device(s)
[[09:45:25]] [INFO] Refreshing device list...

Action Log - 2025-06-16 08:10:53
================================================================================

[[08:10:53]] [INFO] Generating execution report...
[[08:10:53]] [SUCCESS] All tests passed successfully!
[[08:10:52]] [SUCCESS] Screenshot refreshed
[[08:10:52]] [INFO] Refreshing screenshot...
[[08:10:48]] [SUCCESS] Screenshot refreshed successfully
[[08:10:48]] [SUCCESS] Screenshot refreshed successfully
[[08:10:48]] [INFO] Executing action 512/512: Wait till accessibility_id=txtHomeAccountCtaSignIn
[[08:10:47]] [SUCCESS] Screenshot refreshed
[[08:10:47]] [INFO] Refreshing screenshot...
[[08:10:42]] [SUCCESS] Screenshot refreshed successfully
[[08:10:42]] [SUCCESS] Screenshot refreshed successfully
[[08:10:42]] [INFO] Executing action 511/512: Tap on Text: "out"
[[08:10:42]] [SUCCESS] Screenshot refreshed
[[08:10:42]] [INFO] Refreshing screenshot...
[[08:10:38]] [SUCCESS] Screenshot refreshed successfully
[[08:10:38]] [SUCCESS] Screenshot refreshed successfully
[[08:10:38]] [INFO] Executing action 510/512: Swipe from (50%, 70%) to (50%, 30%)
[[08:10:38]] [SUCCESS] Screenshot refreshed
[[08:10:38]] [INFO] Refreshing screenshot...
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:35]] [SUCCESS] Screenshot refreshed successfully
[[08:10:34]] [INFO] Executing action 509/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:10:34]] [SUCCESS] Screenshot refreshed
[[08:10:34]] [INFO] Refreshing screenshot...
[[08:10:31]] [SUCCESS] Screenshot refreshed successfully
[[08:10:31]] [SUCCESS] Screenshot refreshed successfully
[[08:10:30]] [INFO] Executing action 508/512: Swipe from (90%, 20%) to (30%, 20%)
[[08:10:30]] [SUCCESS] Screenshot refreshed
[[08:10:30]] [INFO] Refreshing screenshot...
[[08:10:27]] [SUCCESS] Screenshot refreshed successfully
[[08:10:27]] [SUCCESS] Screenshot refreshed successfully
[[08:10:26]] [INFO] Executing action 507/512: Swipe from (90%, 20%) to (30%, 20%)
[[08:10:26]] [SUCCESS] Screenshot refreshed
[[08:10:26]] [INFO] Refreshing screenshot...
[[08:10:22]] [SUCCESS] Screenshot refreshed successfully
[[08:10:22]] [SUCCESS] Screenshot refreshed successfully
[[08:10:22]] [INFO] Executing action 506/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[08:10:21]] [SUCCESS] Screenshot refreshed
[[08:10:21]] [INFO] Refreshing screenshot...
[[08:10:17]] [SUCCESS] Screenshot refreshed successfully
[[08:10:17]] [SUCCESS] Screenshot refreshed successfully
[[08:10:17]] [INFO] Executing action 505/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[08:10:16]] [SUCCESS] Screenshot refreshed
[[08:10:16]] [INFO] Refreshing screenshot...
[[08:10:12]] [INFO] Executing action 504/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[08:10:12]] [SUCCESS] Screenshot refreshed successfully
[[08:10:12]] [SUCCESS] Screenshot refreshed successfully
[[08:10:12]] [SUCCESS] Screenshot refreshed
[[08:10:12]] [INFO] Refreshing screenshot...
[[08:10:08]] [SUCCESS] Screenshot refreshed successfully
[[08:10:08]] [SUCCESS] Screenshot refreshed successfully
[[08:10:08]] [INFO] Executing action 503/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:10:07]] [SUCCESS] Screenshot refreshed
[[08:10:07]] [INFO] Refreshing screenshot...
[[08:10:04]] [SUCCESS] Screenshot refreshed successfully
[[08:10:04]] [SUCCESS] Screenshot refreshed successfully
[[08:10:04]] [INFO] Executing action 502/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[08:10:03]] [SUCCESS] Screenshot refreshed
[[08:10:03]] [INFO] Refreshing screenshot...
[[08:10:00]] [SUCCESS] Screenshot refreshed successfully
[[08:10:00]] [SUCCESS] Screenshot refreshed successfully
[[08:09:59]] [INFO] Executing action 501/512: iOS Function: text
[[08:09:59]] [SUCCESS] Screenshot refreshed
[[08:09:59]] [INFO] Refreshing screenshot...
[[08:09:54]] [SUCCESS] Screenshot refreshed successfully
[[08:09:54]] [SUCCESS] Screenshot refreshed successfully
[[08:09:53]] [INFO] Executing action 500/512: Tap on Text: "Find"
[[08:09:53]] [SUCCESS] Screenshot refreshed
[[08:09:53]] [INFO] Refreshing screenshot...
[[08:09:50]] [SUCCESS] Screenshot refreshed successfully
[[08:09:50]] [SUCCESS] Screenshot refreshed successfully
[[08:09:49]] [INFO] Executing action 499/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[08:09:49]] [SUCCESS] Screenshot refreshed
[[08:09:49]] [INFO] Refreshing screenshot...
[[08:09:44]] [SUCCESS] Screenshot refreshed successfully
[[08:09:44]] [SUCCESS] Screenshot refreshed successfully
[[08:09:44]] [INFO] Executing action 498/512: iOS Function: text
[[08:09:44]] [SUCCESS] Screenshot refreshed
[[08:09:44]] [INFO] Refreshing screenshot...
[[08:09:40]] [SUCCESS] Screenshot refreshed successfully
[[08:09:40]] [SUCCESS] Screenshot refreshed successfully
[[08:09:40]] [INFO] Executing action 497/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[08:09:39]] [SUCCESS] Screenshot refreshed
[[08:09:39]] [INFO] Refreshing screenshot...
[[08:09:35]] [SUCCESS] Screenshot refreshed successfully
[[08:09:35]] [SUCCESS] Screenshot refreshed successfully
[[08:09:35]] [INFO] Executing action 496/512: iOS Function: text
[[08:09:34]] [SUCCESS] Screenshot refreshed
[[08:09:34]] [INFO] Refreshing screenshot...
[[08:09:31]] [SUCCESS] Screenshot refreshed successfully
[[08:09:31]] [SUCCESS] Screenshot refreshed successfully
[[08:09:31]] [INFO] Executing action 495/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[08:09:30]] [SUCCESS] Screenshot refreshed
[[08:09:30]] [INFO] Refreshing screenshot...
[[08:09:28]] [SUCCESS] Screenshot refreshed successfully
[[08:09:28]] [SUCCESS] Screenshot refreshed successfully
[[08:09:27]] [INFO] Executing action 494/512: iOS Function: alert_accept
[[08:09:27]] [SUCCESS] Screenshot refreshed
[[08:09:27]] [INFO] Refreshing screenshot...
[[08:09:22]] [SUCCESS] Screenshot refreshed successfully
[[08:09:22]] [SUCCESS] Screenshot refreshed successfully
[[08:09:21]] [INFO] Executing action 493/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[08:09:21]] [SUCCESS] Screenshot refreshed
[[08:09:21]] [INFO] Refreshing screenshot...
[[08:09:17]] [INFO] Executing action 492/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:09:17]] [SUCCESS] Screenshot refreshed successfully
[[08:09:17]] [SUCCESS] Screenshot refreshed successfully
[[08:09:17]] [SUCCESS] Screenshot refreshed
[[08:09:17]] [INFO] Refreshing screenshot...
[[08:09:11]] [INFO] Executing action 491/512: Swipe from (5%, 50%) to (90%, 50%)
[[08:09:11]] [SUCCESS] Screenshot refreshed successfully
[[08:09:11]] [SUCCESS] Screenshot refreshed successfully
[[08:09:11]] [SUCCESS] Screenshot refreshed
[[08:09:11]] [INFO] Refreshing screenshot...
[[08:09:05]] [INFO] Executing action 490/512: Swipe from (5%, 50%) to (90%, 50%)
[[08:09:05]] [SUCCESS] Screenshot refreshed successfully
[[08:09:05]] [SUCCESS] Screenshot refreshed successfully
[[08:09:05]] [SUCCESS] Screenshot refreshed
[[08:09:05]] [INFO] Refreshing screenshot...
[[08:09:00]] [INFO] Executing action 489/512: Tap on Text: "Months"
[[08:09:00]] [SUCCESS] Screenshot refreshed successfully
[[08:09:00]] [SUCCESS] Screenshot refreshed successfully
[[08:09:00]] [SUCCESS] Screenshot refreshed
[[08:09:00]] [INFO] Refreshing screenshot...
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [SUCCESS] Screenshot refreshed successfully
[[08:08:56]] [INFO] Executing action 488/512: Tap on Text: "Age"
[[08:08:55]] [SUCCESS] Screenshot refreshed
[[08:08:55]] [INFO] Refreshing screenshot...
[[08:08:51]] [SUCCESS] Screenshot refreshed successfully
[[08:08:51]] [SUCCESS] Screenshot refreshed successfully
[[08:08:51]] [INFO] Executing action 487/512: Tap on Text: "Toys"
[[08:08:50]] [SUCCESS] Screenshot refreshed
[[08:08:50]] [INFO] Refreshing screenshot...
[[08:08:47]] [SUCCESS] Screenshot refreshed successfully
[[08:08:47]] [SUCCESS] Screenshot refreshed successfully
[[08:08:47]] [INFO] Executing action 486/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[08:08:46]] [SUCCESS] Screenshot refreshed
[[08:08:46]] [INFO] Refreshing screenshot...
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:43]] [SUCCESS] Screenshot refreshed successfully
[[08:08:41]] [INFO] Executing action 485/512: Restart app: env[appid]
[[08:08:41]] [SUCCESS] Screenshot refreshed
[[08:08:41]] [INFO] Refreshing screenshot...
[[08:08:40]] [SUCCESS] Screenshot refreshed
[[08:08:40]] [INFO] Refreshing screenshot...
[[08:08:25]] [SUCCESS] Screenshot refreshed successfully
[[08:08:25]] [SUCCESS] Screenshot refreshed successfully
[[08:08:25]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[08:08:24]] [SUCCESS] Screenshot refreshed
[[08:08:24]] [INFO] Refreshing screenshot...
[[08:07:42]] [SUCCESS] Screenshot refreshed successfully
[[08:07:42]] [SUCCESS] Screenshot refreshed successfully
[[08:07:41]] [INFO] Executing Multi Step action step 9/10: Swipe from (50%, 80%) to (50%, 10%)
[[08:07:41]] [SUCCESS] Screenshot refreshed
[[08:07:41]] [INFO] Refreshing screenshot...
[[08:07:25]] [SUCCESS] Screenshot refreshed successfully
[[08:07:25]] [SUCCESS] Screenshot refreshed successfully
[[08:07:25]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to previous page"]
[[08:07:24]] [SUCCESS] Screenshot refreshed
[[08:07:24]] [INFO] Refreshing screenshot...
[[08:06:42]] [SUCCESS] Screenshot refreshed successfully
[[08:06:42]] [SUCCESS] Screenshot refreshed successfully
[[08:06:41]] [INFO] Executing Multi Step action step 7/10: Swipe from (50%, 80%) to (50%, 10%)
[[08:06:41]] [SUCCESS] Screenshot refreshed
[[08:06:41]] [INFO] Refreshing screenshot...
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [SUCCESS] Screenshot refreshed successfully
[[08:06:25]] [INFO] Executing Multi Step action step 6/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[08:06:24]] [SUCCESS] Screenshot refreshed
[[08:06:24]] [INFO] Refreshing screenshot...
[[08:05:42]] [SUCCESS] Screenshot refreshed successfully
[[08:05:42]] [SUCCESS] Screenshot refreshed successfully
[[08:05:42]] [INFO] Executing Multi Step action step 5/10: Swipe from (50%, 80%) to (50%, 10%)
[[08:05:41]] [SUCCESS] Screenshot refreshed
[[08:05:41]] [INFO] Refreshing screenshot...
[[08:05:25]] [SUCCESS] Screenshot refreshed successfully
[[08:05:25]] [SUCCESS] Screenshot refreshed successfully
[[08:05:25]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[08:05:25]] [SUCCESS] Screenshot refreshed
[[08:05:25]] [INFO] Refreshing screenshot...
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [SUCCESS] Screenshot refreshed successfully
[[08:04:42]] [INFO] Executing Multi Step action step 3/10: Swipe from (50%, 80%) to (50%, 10%)
[[08:04:42]] [SUCCESS] Screenshot refreshed
[[08:04:42]] [INFO] Refreshing screenshot...
[[08:04:25]] [SUCCESS] Screenshot refreshed successfully
[[08:04:25]] [SUCCESS] Screenshot refreshed successfully
[[08:04:25]] [INFO] Executing Multi Step action step 2/10: Tap on element with xpath: //XCUIElementTypeButton[@name="Go to next page"]
[[08:04:24]] [SUCCESS] Screenshot refreshed
[[08:04:24]] [INFO] Refreshing screenshot...
[[08:03:40]] [SUCCESS] Screenshot refreshed successfully
[[08:03:40]] [SUCCESS] Screenshot refreshed successfully
[[08:03:39]] [INFO] Executing Multi Step action step 1/10: Swipe from (50%, 80%) to (50%, 10%)
[[08:03:39]] [INFO] Loaded 10 steps from test case: Click_Paginations
[[08:03:39]] [INFO] Loading steps for Multi Step action: Click_Paginations
[[08:03:39]] [INFO] Executing action 484/512: Execute Test Case: Click_Paginations (10 steps)
[[08:03:39]] [SUCCESS] Screenshot refreshed
[[08:03:39]] [INFO] Refreshing screenshot...
[[08:03:35]] [SUCCESS] Screenshot refreshed successfully
[[08:03:35]] [SUCCESS] Screenshot refreshed successfully
[[08:03:35]] [INFO] Executing action 483/512: iOS Function: text
[[08:03:34]] [SUCCESS] Screenshot refreshed
[[08:03:34]] [INFO] Refreshing screenshot...
[[08:03:29]] [SUCCESS] Screenshot refreshed successfully
[[08:03:29]] [SUCCESS] Screenshot refreshed successfully
[[08:03:28]] [INFO] Executing action 482/512: Tap on Text: "Find"
[[08:03:28]] [SUCCESS] Screenshot refreshed
[[08:03:28]] [INFO] Refreshing screenshot...
[[08:03:24]] [SUCCESS] Screenshot refreshed successfully
[[08:03:24]] [SUCCESS] Screenshot refreshed successfully
[[08:03:24]] [INFO] Executing action 481/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[08:03:23]] [SUCCESS] Screenshot refreshed
[[08:03:23]] [INFO] Refreshing screenshot...
[[08:03:22]] [INFO] Executing action 480/512: Launch app: env[appid]
[[08:03:21]] [SUCCESS] Screenshot refreshed successfully
[[08:03:21]] [SUCCESS] Screenshot refreshed successfully
[[08:03:21]] [SUCCESS] Screenshot refreshed
[[08:03:21]] [INFO] Refreshing screenshot...
[[08:03:17]] [SUCCESS] Screenshot refreshed successfully
[[08:03:17]] [SUCCESS] Screenshot refreshed successfully
[[08:03:17]] [INFO] Executing action 479/512: Tap on Text: "+61"
[[08:03:16]] [SUCCESS] Screenshot refreshed
[[08:03:16]] [INFO] Refreshing screenshot...
[[08:03:12]] [SUCCESS] Screenshot refreshed successfully
[[08:03:12]] [SUCCESS] Screenshot refreshed successfully
[[08:03:12]] [INFO] Executing action 478/512: Tap on Text: "1800"
[[08:03:11]] [SUCCESS] Screenshot refreshed
[[08:03:11]] [INFO] Refreshing screenshot...
[[08:03:07]] [SUCCESS] Screenshot refreshed successfully
[[08:03:07]] [SUCCESS] Screenshot refreshed successfully
[[08:03:06]] [INFO] Executing action 477/512: Tap on Text: "click"
[[08:03:06]] [SUCCESS] Screenshot refreshed
[[08:03:06]] [INFO] Refreshing screenshot...
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [SUCCESS] Screenshot refreshed successfully
[[08:02:38]] [INFO] Executing action 476/512: Swipe from (50%, 70%) to (50%, 30%)
[[08:02:37]] [SUCCESS] Screenshot refreshed
[[08:02:37]] [INFO] Refreshing screenshot...
[[08:02:33]] [INFO] Executing action 475/512: Tap on Text: "FAQ"
[[08:02:33]] [SUCCESS] Screenshot refreshed successfully
[[08:02:33]] [SUCCESS] Screenshot refreshed successfully
[[08:02:33]] [SUCCESS] Screenshot refreshed
[[08:02:33]] [INFO] Refreshing screenshot...
[[08:02:28]] [SUCCESS] Screenshot refreshed successfully
[[08:02:28]] [SUCCESS] Screenshot refreshed successfully
[[08:02:28]] [INFO] Executing action 474/512: Tap on Text: "Help"
[[08:02:28]] [SUCCESS] Screenshot refreshed
[[08:02:28]] [INFO] Refreshing screenshot...
[[08:02:25]] [SUCCESS] Screenshot refreshed successfully
[[08:02:25]] [SUCCESS] Screenshot refreshed successfully
[[08:02:24]] [INFO] Executing action 473/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[08:02:24]] [SUCCESS] Screenshot refreshed
[[08:02:24]] [INFO] Refreshing screenshot...
[[08:02:19]] [SUCCESS] Screenshot refreshed successfully
[[08:02:19]] [SUCCESS] Screenshot refreshed successfully
[[08:02:19]] [INFO] Executing action 472/512: Restart app: env[appid]
[[08:02:18]] [SUCCESS] Screenshot refreshed
[[08:02:18]] [INFO] Refreshing screenshot...
[[08:02:12]] [SUCCESS] Screenshot refreshed successfully
[[08:02:12]] [SUCCESS] Screenshot refreshed successfully
[[08:02:12]] [INFO] Executing action 471/512: Tap on Text: "Done"
[[08:02:12]] [SUCCESS] Screenshot refreshed
[[08:02:12]] [INFO] Refreshing screenshot...
[[08:01:57]] [SUCCESS] Screenshot refreshed successfully
[[08:01:57]] [SUCCESS] Screenshot refreshed successfully
[[08:01:57]] [INFO] Executing action 470/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Instruction Manual"]
[[08:01:56]] [SUCCESS] Screenshot refreshed
[[08:01:56]] [INFO] Refreshing screenshot...
[[08:01:41]] [SUCCESS] Screenshot refreshed successfully
[[08:01:41]] [SUCCESS] Screenshot refreshed successfully
[[08:01:41]] [INFO] Executing action 469/512: Swipe from (50%, 80%) to (50%, 10%)
[[08:01:40]] [SUCCESS] Screenshot refreshed
[[08:01:40]] [INFO] Refreshing screenshot...
[[08:01:24]] [SUCCESS] Screenshot refreshed successfully
[[08:01:24]] [SUCCESS] Screenshot refreshed successfully
[[08:01:23]] [INFO] Executing action 468/512: Swipe from (50%, 70%) to (50%, 10%)
[[08:01:23]] [SUCCESS] Screenshot refreshed
[[08:01:23]] [INFO] Refreshing screenshot...
[[08:01:19]] [SUCCESS] Screenshot refreshed successfully
[[08:01:19]] [SUCCESS] Screenshot refreshed successfully
[[08:01:19]] [INFO] Executing action 467/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[08:01:19]] [SUCCESS] Screenshot refreshed
[[08:01:19]] [INFO] Refreshing screenshot...
[[08:01:15]] [SUCCESS] Screenshot refreshed successfully
[[08:01:15]] [SUCCESS] Screenshot refreshed successfully
[[08:01:15]] [INFO] Executing action 466/512: iOS Function: text
[[08:01:14]] [SUCCESS] Screenshot refreshed
[[08:01:14]] [INFO] Refreshing screenshot...
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:09]] [SUCCESS] Screenshot refreshed successfully
[[08:01:08]] [INFO] Executing action 465/512: Tap on Text: "Find"
[[08:01:08]] [SUCCESS] Screenshot refreshed
[[08:01:08]] [INFO] Refreshing screenshot...
[[08:00:53]] [SUCCESS] Screenshot refreshed successfully
[[08:00:53]] [SUCCESS] Screenshot refreshed successfully
[[08:00:53]] [INFO] Executing action 464/512: Restart app: env[appid]
[[08:00:52]] [SUCCESS] Screenshot refreshed
[[08:00:52]] [INFO] Refreshing screenshot...
[[08:00:48]] [INFO] Executing action 463/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[08:00:48]] [SUCCESS] Screenshot refreshed successfully
[[08:00:48]] [SUCCESS] Screenshot refreshed successfully
[[08:00:48]] [SUCCESS] Screenshot refreshed
[[08:00:48]] [INFO] Refreshing screenshot...
[[08:00:45]] [SUCCESS] Screenshot refreshed successfully
[[08:00:45]] [SUCCESS] Screenshot refreshed successfully
[[08:00:44]] [INFO] Executing action 462/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[08:00:44]] [SUCCESS] Screenshot refreshed
[[08:00:44]] [INFO] Refreshing screenshot...
[[08:00:39]] [SUCCESS] Screenshot refreshed successfully
[[08:00:39]] [SUCCESS] Screenshot refreshed successfully
[[08:00:38]] [INFO] Executing action 461/512: Restart app: env[appid]
[[08:00:38]] [SUCCESS] Screenshot refreshed
[[08:00:38]] [INFO] Refreshing screenshot...
[[08:00:31]] [SUCCESS] Screenshot refreshed successfully
[[08:00:31]] [SUCCESS] Screenshot refreshed successfully
[[08:00:31]] [INFO] Executing action 460/512: Tap on element with accessibility_id: Add to bag
[[08:00:31]] [SUCCESS] Screenshot refreshed
[[08:00:31]] [INFO] Refreshing screenshot...
[[08:00:27]] [SUCCESS] Screenshot refreshed successfully
[[08:00:27]] [SUCCESS] Screenshot refreshed successfully
[[08:00:27]] [INFO] Executing action 459/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[08:00:26]] [SUCCESS] Screenshot refreshed
[[08:00:26]] [INFO] Refreshing screenshot...
[[08:00:21]] [SUCCESS] Screenshot refreshed successfully
[[08:00:21]] [SUCCESS] Screenshot refreshed successfully
[[08:00:20]] [INFO] Executing action 458/512: swipeTillVisible action
[[08:00:20]] [SUCCESS] Screenshot refreshed
[[08:00:20]] [INFO] Refreshing screenshot...
[[08:00:18]] [SUCCESS] Screenshot refreshed successfully
[[08:00:18]] [SUCCESS] Screenshot refreshed successfully
[[08:00:16]] [INFO] Executing action 457/512: Tap on element with xpath: (//XCUIElementTypeScrollView/following-sibling::XCUIElementTypeImage[contains(@name,"$")])[1]
[[08:00:16]] [SUCCESS] Screenshot refreshed
[[08:00:16]] [INFO] Refreshing screenshot...
[[08:00:11]] [SUCCESS] Screenshot refreshed successfully
[[08:00:11]] [SUCCESS] Screenshot refreshed successfully
[[08:00:11]] [INFO] Executing action 456/512: Tap on element with xpath: (//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]
[[08:00:10]] [SUCCESS] Screenshot refreshed
[[08:00:10]] [INFO] Refreshing screenshot...
[[08:00:07]] [SUCCESS] Screenshot refreshed successfully
[[08:00:07]] [SUCCESS] Screenshot refreshed successfully
[[08:00:06]] [INFO] Executing action 455/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Home & Living"]
[[08:00:06]] [SUCCESS] Screenshot refreshed
[[08:00:06]] [INFO] Refreshing screenshot...
[[08:00:00]] [SUCCESS] Screenshot refreshed successfully
[[08:00:00]] [SUCCESS] Screenshot refreshed successfully
[[07:59:59]] [INFO] Executing action 454/512: swipeTillVisible action
[[07:59:59]] [SUCCESS] Screenshot refreshed
[[07:59:59]] [INFO] Refreshing screenshot...
[[07:59:55]] [SUCCESS] Screenshot refreshed successfully
[[07:59:55]] [SUCCESS] Screenshot refreshed successfully
[[07:59:55]] [INFO] Executing action 453/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"1 of 5")]
[[07:59:54]] [SUCCESS] Screenshot refreshed
[[07:59:54]] [INFO] Refreshing screenshot...
[[07:59:50]] [SUCCESS] Screenshot refreshed successfully
[[07:59:50]] [SUCCESS] Screenshot refreshed successfully
[[07:59:50]] [INFO] Executing action 452/512: Tap on image: banner-close-updated.png
[[07:59:50]] [SUCCESS] Screenshot refreshed
[[07:59:50]] [INFO] Refreshing screenshot...
[[07:59:46]] [SUCCESS] Screenshot refreshed successfully
[[07:59:46]] [SUCCESS] Screenshot refreshed successfully
[[07:59:46]] [INFO] Executing action 451/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:59:45]] [SUCCESS] Screenshot refreshed
[[07:59:45]] [INFO] Refreshing screenshot...
[[07:59:42]] [SUCCESS] Screenshot refreshed successfully
[[07:59:42]] [SUCCESS] Screenshot refreshed successfully
[[07:59:42]] [INFO] Executing action 450/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Decrease quantity"]
[[07:59:41]] [SUCCESS] Screenshot refreshed
[[07:59:41]] [INFO] Refreshing screenshot...
[[07:59:38]] [SUCCESS] Screenshot refreshed successfully
[[07:59:38]] [SUCCESS] Screenshot refreshed successfully
[[07:59:38]] [INFO] Executing action 449/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Increase quantity"]
[[07:59:37]] [SUCCESS] Screenshot refreshed
[[07:59:37]] [INFO] Refreshing screenshot...
[[07:59:34]] [SUCCESS] Screenshot refreshed successfully
[[07:59:34]] [SUCCESS] Screenshot refreshed successfully
[[07:59:34]] [INFO] Executing action 448/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[07:59:33]] [SUCCESS] Screenshot refreshed
[[07:59:33]] [INFO] Refreshing screenshot...
[[07:59:28]] [SUCCESS] Screenshot refreshed successfully
[[07:59:28]] [SUCCESS] Screenshot refreshed successfully
[[07:59:27]] [INFO] Executing action 447/512: Tap on element with accessibility_id: Add to bag
[[07:59:27]] [SUCCESS] Screenshot refreshed
[[07:59:27]] [INFO] Refreshing screenshot...
[[07:59:24]] [SUCCESS] Screenshot refreshed successfully
[[07:59:24]] [SUCCESS] Screenshot refreshed successfully
[[07:59:23]] [INFO] Executing action 446/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[07:59:23]] [SUCCESS] Screenshot refreshed
[[07:59:23]] [INFO] Refreshing screenshot...
[[07:59:18]] [SUCCESS] Screenshot refreshed successfully
[[07:59:18]] [SUCCESS] Screenshot refreshed successfully
[[07:59:17]] [INFO] Executing action 445/512: Tap on Text: "List"
[[07:59:16]] [SUCCESS] Screenshot refreshed
[[07:59:16]] [INFO] Refreshing screenshot...
[[07:59:12]] [SUCCESS] Screenshot refreshed successfully
[[07:59:12]] [SUCCESS] Screenshot refreshed successfully
[[07:59:11]] [INFO] Executing action 444/512: Tap on image: env[catalogue-menu-img]
[[07:59:10]] [SUCCESS] Screenshot refreshed
[[07:59:10]] [INFO] Refreshing screenshot...
[[07:59:05]] [SUCCESS] Screenshot refreshed successfully
[[07:59:05]] [SUCCESS] Screenshot refreshed successfully
[[07:59:05]] [INFO] Executing action 443/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[07:59:04]] [SUCCESS] Screenshot refreshed
[[07:59:04]] [INFO] Refreshing screenshot...
[[07:59:00]] [SUCCESS] Screenshot refreshed successfully
[[07:59:00]] [SUCCESS] Screenshot refreshed successfully
[[07:59:00]] [INFO] Executing action 442/512: Tap on Text: "Catalogue"
[[07:58:59]] [SUCCESS] Screenshot refreshed
[[07:58:59]] [INFO] Refreshing screenshot...
[[07:58:56]] [SUCCESS] Screenshot refreshed successfully
[[07:58:56]] [SUCCESS] Screenshot refreshed successfully
[[07:58:55]] [INFO] Executing action 441/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[07:58:55]] [SUCCESS] Screenshot refreshed
[[07:58:55]] [INFO] Refreshing screenshot...
[[07:58:49]] [SUCCESS] Screenshot refreshed successfully
[[07:58:49]] [SUCCESS] Screenshot refreshed successfully
[[07:58:49]] [INFO] Executing action 440/512: Tap on element with accessibility_id: Add to bag
[[07:58:48]] [SUCCESS] Screenshot refreshed
[[07:58:48]] [INFO] Refreshing screenshot...
[[07:58:45]] [SUCCESS] Screenshot refreshed successfully
[[07:58:45]] [SUCCESS] Screenshot refreshed successfully
[[07:58:45]] [INFO] Executing action 439/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Kmart Catalogue"]//XCUIElementTypeStaticText[contains(@name,"$")])[1]
[[07:58:45]] [SUCCESS] Screenshot refreshed
[[07:58:45]] [INFO] Refreshing screenshot...
[[07:58:39]] [SUCCESS] Screenshot refreshed successfully
[[07:58:39]] [SUCCESS] Screenshot refreshed successfully
[[07:58:38]] [INFO] Executing action 438/512: Tap on Text: "List"
[[07:58:38]] [SUCCESS] Screenshot refreshed
[[07:58:38]] [INFO] Refreshing screenshot...
[[07:58:34]] [SUCCESS] Screenshot refreshed successfully
[[07:58:34]] [SUCCESS] Screenshot refreshed successfully
[[07:58:32]] [INFO] Executing action 437/512: Tap on image: env[catalogue-menu-img]
[[07:58:32]] [SUCCESS] Screenshot refreshed
[[07:58:32]] [INFO] Refreshing screenshot...
[[07:58:27]] [SUCCESS] Screenshot refreshed successfully
[[07:58:27]] [SUCCESS] Screenshot refreshed successfully
[[07:58:26]] [INFO] Executing action 436/512: If exists: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]" (timeout: 20s) → Then click element: xpath="//XCUIElementTypeOther[@name="Kmart Catalogue"]/XCUIElementTypeImage[1]"
[[07:58:26]] [SUCCESS] Screenshot refreshed
[[07:58:26]] [INFO] Refreshing screenshot...
[[07:58:21]] [SUCCESS] Screenshot refreshed successfully
[[07:58:21]] [SUCCESS] Screenshot refreshed successfully
[[07:58:21]] [INFO] Executing action 435/512: Tap on Text: "Catalogue"
[[07:58:21]] [SUCCESS] Screenshot refreshed
[[07:58:21]] [INFO] Refreshing screenshot...
[[07:58:18]] [SUCCESS] Screenshot refreshed successfully
[[07:58:18]] [SUCCESS] Screenshot refreshed successfully
[[07:58:17]] [INFO] Executing action 434/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[07:58:17]] [SUCCESS] Screenshot refreshed
[[07:58:17]] [INFO] Refreshing screenshot...
[[07:58:14]] [SUCCESS] Screenshot refreshed successfully
[[07:58:14]] [SUCCESS] Screenshot refreshed successfully
[[07:58:14]] [INFO] Executing action 433/512: Check if element with xpath="//XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]" exists
[[07:58:13]] [SUCCESS] Screenshot refreshed
[[07:58:13]] [INFO] Refreshing screenshot...
[[07:58:10]] [SUCCESS] Screenshot refreshed successfully
[[07:58:10]] [SUCCESS] Screenshot refreshed successfully
[[07:58:10]] [INFO] Executing action 432/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="https://www.kmart.com.au"]
[[07:58:09]] [SUCCESS] Screenshot refreshed
[[07:58:09]] [INFO] Refreshing screenshot...
[[07:58:06]] [SUCCESS] Screenshot refreshed successfully
[[07:58:06]] [SUCCESS] Screenshot refreshed successfully
[[07:58:06]] [INFO] Executing action 431/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:58:05]] [SUCCESS] Screenshot refreshed
[[07:58:05]] [INFO] Refreshing screenshot...
[[07:58:01]] [INFO] Executing action 430/512: iOS Function: text
[[07:58:01]] [SUCCESS] Screenshot refreshed successfully
[[07:58:01]] [SUCCESS] Screenshot refreshed successfully
[[07:58:01]] [SUCCESS] Screenshot refreshed
[[07:58:01]] [INFO] Refreshing screenshot...
[[07:57:57]] [SUCCESS] Screenshot refreshed successfully
[[07:57:57]] [SUCCESS] Screenshot refreshed successfully
[[07:57:57]] [INFO] Executing action 429/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="TabBarItemTitle"]
[[07:57:57]] [SUCCESS] Screenshot refreshed
[[07:57:57]] [INFO] Refreshing screenshot...
[[07:57:53]] [SUCCESS] Screenshot refreshed successfully
[[07:57:53]] [SUCCESS] Screenshot refreshed successfully
[[07:57:53]] [INFO] Executing action 428/512: Restart app: com.apple.mobilesafari
[[07:57:52]] [SUCCESS] Screenshot refreshed
[[07:57:52]] [INFO] Refreshing screenshot...
[[07:57:48]] [SUCCESS] Screenshot refreshed successfully
[[07:57:48]] [SUCCESS] Screenshot refreshed successfully
[[07:57:48]] [INFO] Executing action 427/512: Tap on Text: "out"
[[07:57:47]] [SUCCESS] Screenshot refreshed
[[07:57:47]] [INFO] Refreshing screenshot...
[[07:57:43]] [SUCCESS] Screenshot refreshed successfully
[[07:57:43]] [SUCCESS] Screenshot refreshed successfully
[[07:57:43]] [INFO] Executing action 426/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:57:43]] [SUCCESS] Screenshot refreshed
[[07:57:43]] [INFO] Refreshing screenshot...
[[07:57:40]] [SUCCESS] Screenshot refreshed successfully
[[07:57:40]] [SUCCESS] Screenshot refreshed successfully
[[07:57:39]] [INFO] Executing action 425/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"5 of 5")]
[[07:57:38]] [SUCCESS] Screenshot refreshed
[[07:57:38]] [INFO] Refreshing screenshot...
[[07:57:33]] [INFO] Executing action 424/512: Restart app: env[appid]
[[07:57:33]] [SUCCESS] Screenshot refreshed successfully
[[07:57:33]] [SUCCESS] Screenshot refreshed successfully
[[07:57:33]] [SUCCESS] Screenshot refreshed
[[07:57:33]] [INFO] Refreshing screenshot...
[[07:57:26]] [INFO] Executing action 423/512: Wait for 5 ms
[[07:57:26]] [SUCCESS] Screenshot refreshed successfully
[[07:57:26]] [SUCCESS] Screenshot refreshed successfully
[[07:57:26]] [SUCCESS] Screenshot refreshed
[[07:57:26]] [INFO] Refreshing screenshot...
[[07:57:23]] [INFO] Executing action 422/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[07:57:23]] [SUCCESS] Screenshot refreshed successfully
[[07:57:23]] [SUCCESS] Screenshot refreshed successfully
[[07:57:23]] [SUCCESS] Screenshot refreshed
[[07:57:23]] [INFO] Refreshing screenshot...
[[07:57:21]] [SUCCESS] Screenshot refreshed successfully
[[07:57:21]] [SUCCESS] Screenshot refreshed successfully
[[07:57:21]] [INFO] Executing action 421/512: Launch app: com.apple.Preferences
[[07:57:21]] [SUCCESS] Screenshot refreshed
[[07:57:21]] [INFO] Refreshing screenshot...
[[07:57:19]] [SUCCESS] Screenshot refreshed successfully
[[07:57:19]] [SUCCESS] Screenshot refreshed successfully
[[07:57:19]] [INFO] Executing action 420/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:57:19]] [SUCCESS] Screenshot refreshed
[[07:57:19]] [INFO] Refreshing screenshot...
[[07:57:17]] [SUCCESS] Screenshot refreshed successfully
[[07:57:17]] [SUCCESS] Screenshot refreshed successfully
[[07:57:17]] [INFO] Executing action 419/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"4 of 5")]
[[07:57:16]] [SUCCESS] Screenshot refreshed
[[07:57:16]] [INFO] Refreshing screenshot...
[[07:57:14]] [SUCCESS] Screenshot refreshed successfully
[[07:57:14]] [SUCCESS] Screenshot refreshed successfully
[[07:57:14]] [INFO] Executing action 418/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:57:14]] [SUCCESS] Screenshot refreshed
[[07:57:14]] [INFO] Refreshing screenshot...
[[07:57:12]] [SUCCESS] Screenshot refreshed successfully
[[07:57:12]] [SUCCESS] Screenshot refreshed successfully
[[07:57:12]] [INFO] Executing action 417/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"3 of 5")]
[[07:57:11]] [SUCCESS] Screenshot refreshed
[[07:57:11]] [INFO] Refreshing screenshot...
[[07:57:09]] [SUCCESS] Screenshot refreshed successfully
[[07:57:09]] [SUCCESS] Screenshot refreshed successfully
[[07:57:09]] [INFO] Executing action 416/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:57:09]] [SUCCESS] Screenshot refreshed
[[07:57:09]] [INFO] Refreshing screenshot...
[[07:57:07]] [SUCCESS] Screenshot refreshed successfully
[[07:57:07]] [SUCCESS] Screenshot refreshed successfully
[[07:57:07]] [INFO] Executing action 415/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"2 of 5")]
[[07:57:06]] [SUCCESS] Screenshot refreshed
[[07:57:06]] [INFO] Refreshing screenshot...
[[07:57:04]] [SUCCESS] Screenshot refreshed successfully
[[07:57:04]] [SUCCESS] Screenshot refreshed successfully
[[07:57:04]] [INFO] Executing action 414/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtNo internet connection"]" exists
[[07:57:04]] [SUCCESS] Screenshot refreshed
[[07:57:04]] [INFO] Refreshing screenshot...
[[07:56:59]] [INFO] Executing action 413/512: Restart app: env[appid]
[[07:56:59]] [SUCCESS] Screenshot refreshed successfully
[[07:56:59]] [SUCCESS] Screenshot refreshed successfully
[[07:56:58]] [SUCCESS] Screenshot refreshed
[[07:56:58]] [INFO] Refreshing screenshot...
[[07:56:56]] [INFO] Executing action 412/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Wi‑Fi"]
[[07:56:56]] [SUCCESS] Screenshot refreshed successfully
[[07:56:56]] [SUCCESS] Screenshot refreshed successfully
[[07:56:55]] [SUCCESS] Screenshot refreshed
[[07:56:55]] [INFO] Refreshing screenshot...
[[07:56:51]] [INFO] Executing action 411/512: Tap on Text: "Wi-Fi"
[[07:56:51]] [SUCCESS] Screenshot refreshed successfully
[[07:56:51]] [SUCCESS] Screenshot refreshed successfully
[[07:56:51]] [SUCCESS] Screenshot refreshed
[[07:56:51]] [INFO] Refreshing screenshot...
[[07:56:48]] [SUCCESS] Screenshot refreshed successfully
[[07:56:48]] [SUCCESS] Screenshot refreshed successfully
[[07:56:48]] [INFO] Executing action 410/512: Launch app: com.apple.Preferences
[[07:56:47]] [SUCCESS] Screenshot refreshed
[[07:56:47]] [INFO] Refreshing screenshot...
[[07:56:46]] [SUCCESS] Screenshot refreshed successfully
[[07:56:46]] [SUCCESS] Screenshot refreshed successfully
[[07:56:45]] [INFO] Executing action 409/512: Terminate app: com.apple.Preferences
[[07:56:45]] [SUCCESS] Screenshot refreshed
[[07:56:45]] [INFO] Refreshing screenshot...
[[07:56:45]] [SUCCESS] Screenshot refreshed
[[07:56:45]] [INFO] Refreshing screenshot...
[[07:56:41]] [SUCCESS] Screenshot refreshed successfully
[[07:56:41]] [SUCCESS] Screenshot refreshed successfully
[[07:56:41]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:56:41]] [SUCCESS] Screenshot refreshed
[[07:56:41]] [INFO] Refreshing screenshot...
[[07:56:36]] [SUCCESS] Screenshot refreshed successfully
[[07:56:36]] [SUCCESS] Screenshot refreshed successfully
[[07:56:36]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[07:56:36]] [SUCCESS] Screenshot refreshed
[[07:56:36]] [INFO] Refreshing screenshot...
[[07:56:32]] [SUCCESS] Screenshot refreshed successfully
[[07:56:32]] [SUCCESS] Screenshot refreshed successfully
[[07:56:32]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:56:31]] [SUCCESS] Screenshot refreshed
[[07:56:31]] [INFO] Refreshing screenshot...
[[07:56:27]] [SUCCESS] Screenshot refreshed successfully
[[07:56:27]] [SUCCESS] Screenshot refreshed successfully
[[07:56:27]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[07:56:26]] [SUCCESS] Screenshot refreshed
[[07:56:26]] [INFO] Refreshing screenshot...
[[07:56:23]] [SUCCESS] Screenshot refreshed successfully
[[07:56:23]] [SUCCESS] Screenshot refreshed successfully
[[07:56:23]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:56:22]] [SUCCESS] Screenshot refreshed
[[07:56:22]] [INFO] Refreshing screenshot...
[[07:56:17]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:56:17]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[07:56:17]] [SUCCESS] Screenshot refreshed successfully
[[07:56:17]] [SUCCESS] Screenshot refreshed successfully
[[07:56:16]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[07:56:16]] [INFO] Executing action 408/512: Execute Test Case: Kmart-Signin (6 steps)
[[07:56:16]] [SUCCESS] Screenshot refreshed
[[07:56:16]] [INFO] Refreshing screenshot...
[[07:56:14]] [SUCCESS] Screenshot refreshed successfully
[[07:56:14]] [SUCCESS] Screenshot refreshed successfully
[[07:56:13]] [INFO] Executing action 407/512: iOS Function: alert_accept
[[07:56:13]] [SUCCESS] Screenshot refreshed
[[07:56:13]] [INFO] Refreshing screenshot...
[[07:56:09]] [SUCCESS] Screenshot refreshed successfully
[[07:56:09]] [SUCCESS] Screenshot refreshed successfully
[[07:56:09]] [INFO] Executing action 406/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtHomeAccountCtaSignIn"]
[[07:56:08]] [SUCCESS] Screenshot refreshed
[[07:56:08]] [INFO] Refreshing screenshot...
[[07:55:53]] [SUCCESS] Screenshot refreshed successfully
[[07:55:53]] [SUCCESS] Screenshot refreshed successfully
[[07:55:53]] [INFO] Executing action 405/512: Restart app: env[appid]
[[07:55:52]] [SUCCESS] Screenshot refreshed
[[07:55:52]] [INFO] Refreshing screenshot...
[[07:55:52]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[07:55:52]] [INFO] Executing action 404/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[07:55:52]] [SUCCESS] Screenshot refreshed
[[07:55:52]] [INFO] Refreshing screenshot...
[[07:55:48]] [SUCCESS] Screenshot refreshed successfully
[[07:55:48]] [SUCCESS] Screenshot refreshed successfully
[[07:55:48]] [INFO] Executing action 403/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:55:47]] [SUCCESS] Screenshot refreshed
[[07:55:47]] [INFO] Refreshing screenshot...
[[07:55:41]] [SUCCESS] Screenshot refreshed successfully
[[07:55:41]] [SUCCESS] Screenshot refreshed successfully
[[07:55:41]] [INFO] Executing action 402/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:55:40]] [SUCCESS] Screenshot refreshed
[[07:55:40]] [INFO] Refreshing screenshot...
[[07:55:37]] [SUCCESS] Screenshot refreshed successfully
[[07:55:37]] [SUCCESS] Screenshot refreshed successfully
[[07:55:36]] [INFO] Executing action 401/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:55:36]] [SUCCESS] Screenshot refreshed
[[07:55:36]] [INFO] Refreshing screenshot...
[[07:55:34]] [SUCCESS] Screenshot refreshed successfully
[[07:55:34]] [SUCCESS] Screenshot refreshed successfully
[[07:55:34]] [INFO] Executing action 400/512: Add Log: Post code successfully changed from Bag (with screenshot)
[[07:55:34]] [SUCCESS] Screenshot refreshed
[[07:55:34]] [INFO] Refreshing screenshot...
[[07:55:30]] [SUCCESS] Screenshot refreshed successfully
[[07:55:30]] [SUCCESS] Screenshot refreshed successfully
[[07:55:30]] [INFO] Executing action 399/512: Check if image "deliverto3000-se.png" exists on screen
[[07:55:29]] [SUCCESS] Screenshot refreshed
[[07:55:29]] [INFO] Refreshing screenshot...
[[07:55:26]] [SUCCESS] Screenshot refreshed successfully
[[07:55:26]] [SUCCESS] Screenshot refreshed successfully
[[07:55:26]] [INFO] Executing action 398/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:55:25]] [SUCCESS] Screenshot refreshed
[[07:55:25]] [INFO] Refreshing screenshot...
[[07:55:22]] [SUCCESS] Screenshot refreshed successfully
[[07:55:22]] [SUCCESS] Screenshot refreshed successfully
[[07:55:22]] [INFO] Executing action 397/512: Wait till xpath=//XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:55:22]] [SUCCESS] Screenshot refreshed
[[07:55:22]] [INFO] Refreshing screenshot...
[[07:55:18]] [SUCCESS] Screenshot refreshed successfully
[[07:55:18]] [SUCCESS] Screenshot refreshed successfully
[[07:55:18]] [INFO] Executing action 396/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:55:17]] [SUCCESS] Screenshot refreshed
[[07:55:17]] [INFO] Refreshing screenshot...
[[07:55:13]] [SUCCESS] Screenshot refreshed successfully
[[07:55:13]] [SUCCESS] Screenshot refreshed successfully
[[07:55:13]] [INFO] Executing action 395/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:55:13]] [SUCCESS] Screenshot refreshed
[[07:55:13]] [INFO] Refreshing screenshot...
[[07:55:07]] [SUCCESS] Screenshot refreshed successfully
[[07:55:07]] [SUCCESS] Screenshot refreshed successfully
[[07:55:07]] [INFO] Executing action 394/512: Tap on element with accessibility_id: Done
[[07:55:07]] [SUCCESS] Screenshot refreshed
[[07:55:07]] [INFO] Refreshing screenshot...
[[07:55:02]] [SUCCESS] Screenshot refreshed successfully
[[07:55:02]] [SUCCESS] Screenshot refreshed successfully
[[07:55:02]] [INFO] Executing action 393/512: Tap on Text: "VIC"
[[07:55:01]] [SUCCESS] Screenshot refreshed
[[07:55:01]] [INFO] Refreshing screenshot...
[[07:54:55]] [SUCCESS] Screenshot refreshed successfully
[[07:54:55]] [SUCCESS] Screenshot refreshed successfully
[[07:54:55]] [INFO] Executing action 392/512: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "3000"
[[07:54:54]] [SUCCESS] Screenshot refreshed
[[07:54:54]] [INFO] Refreshing screenshot...
[[07:54:49]] [SUCCESS] Screenshot refreshed successfully
[[07:54:49]] [SUCCESS] Screenshot refreshed successfully
[[07:54:49]] [INFO] Executing action 391/512: Tap on element with accessibility_id: delete
[[07:54:48]] [SUCCESS] Screenshot refreshed
[[07:54:48]] [INFO] Refreshing screenshot...
[[07:54:43]] [SUCCESS] Screenshot refreshed successfully
[[07:54:43]] [SUCCESS] Screenshot refreshed successfully
[[07:54:43]] [INFO] Executing action 390/512: Tap on Text: "Nearby"
[[07:54:43]] [SUCCESS] Screenshot refreshed
[[07:54:43]] [INFO] Refreshing screenshot...
[[07:54:40]] [SUCCESS] Screenshot refreshed successfully
[[07:54:40]] [SUCCESS] Screenshot refreshed successfully
[[07:54:40]] [INFO] Executing action 389/512: Wait till xpath=//XCUIElementTypeOther[contains(@value,"Nearby suburb or postcode")]
[[07:54:39]] [SUCCESS] Screenshot refreshed
[[07:54:39]] [INFO] Refreshing screenshot...
[[07:54:35]] [SUCCESS] Screenshot refreshed successfully
[[07:54:35]] [SUCCESS] Screenshot refreshed successfully
[[07:54:35]] [INFO] Executing action 388/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[07:54:35]] [SUCCESS] Screenshot refreshed
[[07:54:35]] [INFO] Refreshing screenshot...
[[07:54:32]] [SUCCESS] Screenshot refreshed successfully
[[07:54:32]] [SUCCESS] Screenshot refreshed successfully
[[07:54:32]] [INFO] Executing action 387/512: Wait till xpath=//XCUIElementTypeOther[@name="Delivery options"]/XCUIElementTypeButton[3]
[[07:54:31]] [SUCCESS] Screenshot refreshed
[[07:54:31]] [INFO] Refreshing screenshot...
[[07:54:28]] [SUCCESS] Screenshot refreshed successfully
[[07:54:28]] [SUCCESS] Screenshot refreshed successfully
[[07:54:27]] [INFO] Executing action 386/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:54:27]] [SUCCESS] Screenshot refreshed
[[07:54:27]] [INFO] Refreshing screenshot...
[[07:54:24]] [SUCCESS] Screenshot refreshed successfully
[[07:54:24]] [SUCCESS] Screenshot refreshed successfully
[[07:54:24]] [INFO] Executing action 385/512: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[07:54:23]] [SUCCESS] Screenshot refreshed
[[07:54:23]] [INFO] Refreshing screenshot...
[[07:54:17]] [SUCCESS] Screenshot refreshed successfully
[[07:54:17]] [SUCCESS] Screenshot refreshed successfully
[[07:54:17]] [INFO] Executing action 384/512: Tap on element with accessibility_id: Add to bag
[[07:54:16]] [SUCCESS] Screenshot refreshed
[[07:54:16]] [INFO] Refreshing screenshot...
[[07:54:10]] [SUCCESS] Screenshot refreshed successfully
[[07:54:10]] [SUCCESS] Screenshot refreshed successfully
[[07:54:09]] [INFO] Executing action 383/512: swipeTillVisible action
[[07:54:09]] [SUCCESS] Screenshot refreshed
[[07:54:09]] [INFO] Refreshing screenshot...
[[07:54:07]] [SUCCESS] Screenshot refreshed successfully
[[07:54:07]] [SUCCESS] Screenshot refreshed successfully
[[07:54:07]] [INFO] Executing action 382/512: Add Log: Post code successfully changed in PDP (with screenshot)
[[07:54:06]] [SUCCESS] Screenshot refreshed
[[07:54:06]] [INFO] Refreshing screenshot...
[[07:54:02]] [SUCCESS] Screenshot refreshed successfully
[[07:54:02]] [SUCCESS] Screenshot refreshed successfully
[[07:54:02]] [INFO] Executing action 381/512: Check if image "shop-broadway-se.png" exists on screen
[[07:54:02]] [SUCCESS] Screenshot refreshed
[[07:54:02]] [INFO] Refreshing screenshot...
[[07:53:57]] [SUCCESS] Screenshot refreshed successfully
[[07:53:57]] [SUCCESS] Screenshot refreshed successfully
[[07:53:57]] [INFO] Executing action 380/512: Tap on Text: "Save"
[[07:53:57]] [SUCCESS] Screenshot refreshed
[[07:53:57]] [INFO] Refreshing screenshot...
[[07:53:53]] [SUCCESS] Screenshot refreshed successfully
[[07:53:53]] [SUCCESS] Screenshot refreshed successfully
[[07:53:53]] [INFO] Executing action 379/512: Wait till accessibility_id=btnSaveOrContinue
[[07:53:52]] [SUCCESS] Screenshot refreshed
[[07:53:52]] [INFO] Refreshing screenshot...
[[07:53:48]] [SUCCESS] Screenshot refreshed successfully
[[07:53:48]] [SUCCESS] Screenshot refreshed successfully
[[07:53:48]] [INFO] Executing action 378/512: Tap on Text: "2000"
[[07:53:47]] [SUCCESS] Screenshot refreshed
[[07:53:47]] [INFO] Refreshing screenshot...
[[07:53:43]] [SUCCESS] Screenshot refreshed successfully
[[07:53:43]] [SUCCESS] Screenshot refreshed successfully
[[07:53:43]] [INFO] Executing action 377/512: textClear action
[[07:53:42]] [SUCCESS] Screenshot refreshed
[[07:53:42]] [INFO] Refreshing screenshot...
[[07:53:37]] [SUCCESS] Screenshot refreshed successfully
[[07:53:37]] [SUCCESS] Screenshot refreshed successfully
[[07:53:37]] [INFO] Executing action 376/512: Tap on element with accessibility_id: Search suburb or postcode
[[07:53:37]] [SUCCESS] Screenshot refreshed
[[07:53:37]] [INFO] Refreshing screenshot...
[[07:53:32]] [SUCCESS] Screenshot refreshed successfully
[[07:53:32]] [SUCCESS] Screenshot refreshed successfully
[[07:53:32]] [INFO] Executing action 375/512: Tap on Text: "Edit"
[[07:53:31]] [SUCCESS] Screenshot refreshed
[[07:53:31]] [INFO] Refreshing screenshot...
[[07:53:27]] [SUCCESS] Screenshot refreshed successfully
[[07:53:27]] [SUCCESS] Screenshot refreshed successfully
[[07:53:27]] [INFO] Executing action 374/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:53:27]] [SUCCESS] Screenshot refreshed
[[07:53:27]] [INFO] Refreshing screenshot...
[[07:53:23]] [SUCCESS] Screenshot refreshed successfully
[[07:53:23]] [SUCCESS] Screenshot refreshed successfully
[[07:53:22]] [INFO] Executing action 373/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:53:22]] [SUCCESS] Screenshot refreshed
[[07:53:22]] [INFO] Refreshing screenshot...
[[07:53:20]] [SUCCESS] Screenshot refreshed successfully
[[07:53:20]] [SUCCESS] Screenshot refreshed successfully
[[07:53:20]] [INFO] Executing action 372/512: Add Log: Post code successfully changed in PLP (with screenshot)
[[07:53:19]] [SUCCESS] Screenshot refreshed
[[07:53:19]] [INFO] Refreshing screenshot...
[[07:53:16]] [SUCCESS] Screenshot refreshed successfully
[[07:53:16]] [SUCCESS] Screenshot refreshed successfully
[[07:53:15]] [INFO] Executing action 371/512: Check if image "SANCTURYLAKE-SE.png" exists on screen
[[07:53:15]] [SUCCESS] Screenshot refreshed
[[07:53:15]] [INFO] Refreshing screenshot...
[[07:53:10]] [SUCCESS] Screenshot refreshed successfully
[[07:53:10]] [SUCCESS] Screenshot refreshed successfully
[[07:53:10]] [INFO] Executing action 370/512: Tap on Text: "Save"
[[07:53:09]] [SUCCESS] Screenshot refreshed
[[07:53:09]] [INFO] Refreshing screenshot...
[[07:53:05]] [SUCCESS] Screenshot refreshed successfully
[[07:53:05]] [SUCCESS] Screenshot refreshed successfully
[[07:53:05]] [INFO] Executing action 369/512: Wait till accessibility_id=btnSaveOrContinue
[[07:53:05]] [SUCCESS] Screenshot refreshed
[[07:53:05]] [INFO] Refreshing screenshot...
[[07:53:00]] [SUCCESS] Screenshot refreshed successfully
[[07:53:00]] [SUCCESS] Screenshot refreshed successfully
[[07:53:00]] [INFO] Executing action 368/512: Tap on Text: "current"
[[07:53:00]] [SUCCESS] Screenshot refreshed
[[07:53:00]] [INFO] Refreshing screenshot...
[[07:52:56]] [SUCCESS] Screenshot refreshed successfully
[[07:52:56]] [SUCCESS] Screenshot refreshed successfully
[[07:52:56]] [INFO] Executing action 367/512: Wait till accessibility_id=btnCurrentLocationButton
[[07:52:55]] [SUCCESS] Screenshot refreshed
[[07:52:55]] [INFO] Refreshing screenshot...
[[07:52:50]] [SUCCESS] Screenshot refreshed successfully
[[07:52:50]] [SUCCESS] Screenshot refreshed successfully
[[07:52:50]] [INFO] Executing action 366/512: Tap on Text: "Edit"
[[07:52:49]] [SUCCESS] Screenshot refreshed
[[07:52:49]] [INFO] Refreshing screenshot...
[[07:52:46]] [SUCCESS] Screenshot refreshed successfully
[[07:52:46]] [SUCCESS] Screenshot refreshed successfully
[[07:52:46]] [INFO] Executing action 365/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:52:45]] [SUCCESS] Screenshot refreshed
[[07:52:45]] [INFO] Refreshing screenshot...
[[07:52:41]] [SUCCESS] Screenshot refreshed successfully
[[07:52:41]] [SUCCESS] Screenshot refreshed successfully
[[07:52:41]] [INFO] Executing action 364/512: iOS Function: text
[[07:52:41]] [SUCCESS] Screenshot refreshed
[[07:52:41]] [INFO] Refreshing screenshot...
[[07:52:36]] [SUCCESS] Screenshot refreshed successfully
[[07:52:36]] [SUCCESS] Screenshot refreshed successfully
[[07:52:35]] [INFO] Executing action 363/512: Tap on Text: "Find"
[[07:52:35]] [SUCCESS] Screenshot refreshed
[[07:52:35]] [INFO] Refreshing screenshot...
[[07:52:33]] [SUCCESS] Screenshot refreshed successfully
[[07:52:33]] [SUCCESS] Screenshot refreshed successfully
[[07:52:33]] [INFO] Executing action 362/512: Add Log: Post code successfully changed to broadway from home page (with screenshot)
[[07:52:32]] [SUCCESS] Screenshot refreshed
[[07:52:32]] [INFO] Refreshing screenshot...
[[07:52:27]] [SUCCESS] Screenshot refreshed successfully
[[07:52:27]] [SUCCESS] Screenshot refreshed successfully
[[07:52:26]] [INFO] Executing action 361/512: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[07:52:26]] [SUCCESS] Screenshot refreshed
[[07:52:26]] [INFO] Refreshing screenshot...
[[07:52:21]] [SUCCESS] Screenshot refreshed successfully
[[07:52:21]] [SUCCESS] Screenshot refreshed successfully
[[07:52:21]] [INFO] Executing action 360/512: Tap on Text: "Save"
[[07:52:21]] [SUCCESS] Screenshot refreshed
[[07:52:21]] [INFO] Refreshing screenshot...
[[07:52:17]] [SUCCESS] Screenshot refreshed successfully
[[07:52:17]] [SUCCESS] Screenshot refreshed successfully
[[07:52:17]] [INFO] Executing action 359/512: Wait till accessibility_id=btnSaveOrContinue
[[07:52:16]] [SUCCESS] Screenshot refreshed
[[07:52:16]] [INFO] Refreshing screenshot...
[[07:52:11]] [SUCCESS] Screenshot refreshed successfully
[[07:52:11]] [SUCCESS] Screenshot refreshed successfully
[[07:52:11]] [INFO] Executing action 358/512: Tap on Text: "2000"
[[07:52:11]] [SUCCESS] Screenshot refreshed
[[07:52:11]] [INFO] Refreshing screenshot...
[[07:52:06]] [SUCCESS] Screenshot refreshed successfully
[[07:52:06]] [SUCCESS] Screenshot refreshed successfully
[[07:52:06]] [INFO] Executing action 357/512: textClear action
[[07:52:06]] [SUCCESS] Screenshot refreshed
[[07:52:06]] [INFO] Refreshing screenshot...
[[07:52:01]] [SUCCESS] Screenshot refreshed successfully
[[07:52:01]] [SUCCESS] Screenshot refreshed successfully
[[07:52:01]] [INFO] Executing action 356/512: Tap on element with accessibility_id: Search suburb or postcode
[[07:52:00]] [SUCCESS] Screenshot refreshed
[[07:52:00]] [INFO] Refreshing screenshot...
[[07:51:57]] [SUCCESS] Screenshot refreshed successfully
[[07:51:57]] [SUCCESS] Screenshot refreshed successfully
[[07:51:57]] [INFO] Executing action 355/512: Tap on element with xpath: //XCUIElementTypeOther[contains(@name,"Deliver")]
[[07:51:56]] [SUCCESS] Screenshot refreshed
[[07:51:56]] [INFO] Refreshing screenshot...
[[07:51:55]] [SUCCESS] Screenshot refreshed successfully
[[07:51:55]] [SUCCESS] Screenshot refreshed successfully
[[07:51:53]] [INFO] Executing action 354/512: Wait till xpath=//XCUIElementTypeOther[contains(@name,"Deliver")]
[[07:51:53]] [SUCCESS] Screenshot refreshed
[[07:51:53]] [INFO] Refreshing screenshot...
[[07:51:52]] [SUCCESS] Screenshot refreshed
[[07:51:52]] [INFO] Refreshing screenshot...
[[07:51:50]] [SUCCESS] Screenshot refreshed successfully
[[07:51:50]] [SUCCESS] Screenshot refreshed successfully
[[07:51:49]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:51:48]] [SUCCESS] Screenshot refreshed
[[07:51:48]] [INFO] Refreshing screenshot...
[[07:51:44]] [SUCCESS] Screenshot refreshed successfully
[[07:51:44]] [SUCCESS] Screenshot refreshed successfully
[[07:51:44]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[07:51:43]] [SUCCESS] Screenshot refreshed
[[07:51:43]] [INFO] Refreshing screenshot...
[[07:51:40]] [SUCCESS] Screenshot refreshed successfully
[[07:51:40]] [SUCCESS] Screenshot refreshed successfully
[[07:51:40]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:51:39]] [SUCCESS] Screenshot refreshed
[[07:51:39]] [INFO] Refreshing screenshot...
[[07:51:35]] [SUCCESS] Screenshot refreshed successfully
[[07:51:35]] [SUCCESS] Screenshot refreshed successfully
[[07:51:35]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[07:51:34]] [SUCCESS] Screenshot refreshed
[[07:51:34]] [INFO] Refreshing screenshot...
[[07:51:30]] [SUCCESS] Screenshot refreshed successfully
[[07:51:30]] [SUCCESS] Screenshot refreshed successfully
[[07:51:30]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:51:30]] [SUCCESS] Screenshot refreshed
[[07:51:30]] [INFO] Refreshing screenshot...
[[07:51:25]] [SUCCESS] Screenshot refreshed successfully
[[07:51:25]] [SUCCESS] Screenshot refreshed successfully
[[07:51:24]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:51:24]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[07:51:24]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[07:51:24]] [INFO] Executing action 353/512: Execute Test Case: Kmart-Signin (6 steps)
[[07:51:24]] [SUCCESS] Screenshot refreshed
[[07:51:24]] [INFO] Refreshing screenshot...
[[07:51:21]] [SUCCESS] Screenshot refreshed successfully
[[07:51:21]] [SUCCESS] Screenshot refreshed successfully
[[07:51:20]] [INFO] Executing action 352/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:51:20]] [SUCCESS] Screenshot refreshed
[[07:51:20]] [INFO] Refreshing screenshot...
[[07:51:18]] [SUCCESS] Screenshot refreshed successfully
[[07:51:18]] [SUCCESS] Screenshot refreshed successfully
[[07:51:17]] [INFO] Executing action 351/512: iOS Function: alert_accept
[[07:51:17]] [SUCCESS] Screenshot refreshed
[[07:51:17]] [INFO] Refreshing screenshot...
[[07:51:11]] [SUCCESS] Screenshot refreshed successfully
[[07:51:11]] [SUCCESS] Screenshot refreshed successfully
[[07:51:10]] [INFO] Executing action 350/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:51:10]] [SUCCESS] Screenshot refreshed
[[07:51:10]] [INFO] Refreshing screenshot...
[[07:50:56]] [SUCCESS] Screenshot refreshed successfully
[[07:50:56]] [SUCCESS] Screenshot refreshed successfully
[[07:50:55]] [INFO] Executing action 349/512: Restart app: env[appid]
[[07:50:55]] [SUCCESS] Screenshot refreshed
[[07:50:55]] [INFO] Refreshing screenshot...
[[07:50:52]] [SUCCESS] Screenshot refreshed successfully
[[07:50:52]] [SUCCESS] Screenshot refreshed successfully
[[07:50:52]] [INFO] Executing action 348/512: Terminate app: env[appid]
[[07:50:51]] [SUCCESS] Screenshot refreshed
[[07:50:51]] [INFO] Refreshing screenshot...
[[07:50:47]] [SUCCESS] Screenshot refreshed successfully
[[07:50:47]] [SUCCESS] Screenshot refreshed successfully
[[07:50:47]] [INFO] Executing action 347/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:50:47]] [SUCCESS] Screenshot refreshed
[[07:50:47]] [INFO] Refreshing screenshot...
[[07:50:43]] [SUCCESS] Screenshot refreshed successfully
[[07:50:43]] [SUCCESS] Screenshot refreshed successfully
[[07:50:43]] [INFO] Executing action 346/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:50:42]] [SUCCESS] Screenshot refreshed
[[07:50:42]] [INFO] Refreshing screenshot...
[[07:50:40]] [SUCCESS] Screenshot refreshed successfully
[[07:50:40]] [SUCCESS] Screenshot refreshed successfully
[[07:50:39]] [INFO] Executing action 345/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:50:39]] [SUCCESS] Screenshot refreshed
[[07:50:39]] [INFO] Refreshing screenshot...
[[07:50:38]] [SUCCESS] Screenshot refreshed
[[07:50:38]] [INFO] Refreshing screenshot...
[[07:50:34]] [SUCCESS] Screenshot refreshed successfully
[[07:50:34]] [SUCCESS] Screenshot refreshed successfully
[[07:50:34]] [INFO] Executing Multi Step action step 41/41: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:50:34]] [SUCCESS] Screenshot refreshed
[[07:50:34]] [INFO] Refreshing screenshot...
[[07:50:30]] [SUCCESS] Screenshot refreshed successfully
[[07:50:30]] [SUCCESS] Screenshot refreshed successfully
[[07:50:30]] [INFO] Executing Multi Step action step 40/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:50:29]] [SUCCESS] Screenshot refreshed
[[07:50:29]] [INFO] Refreshing screenshot...
[[07:50:24]] [SUCCESS] Screenshot refreshed successfully
[[07:50:24]] [SUCCESS] Screenshot refreshed successfully
[[07:50:24]] [INFO] Executing Multi Step action step 39/41: swipeTillVisible action
[[07:50:23]] [SUCCESS] Screenshot refreshed
[[07:50:23]] [INFO] Refreshing screenshot...
[[07:50:20]] [INFO] Executing Multi Step action step 38/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:50:20]] [SUCCESS] Screenshot refreshed successfully
[[07:50:20]] [SUCCESS] Screenshot refreshed successfully
[[07:50:19]] [SUCCESS] Screenshot refreshed
[[07:50:19]] [INFO] Refreshing screenshot...
[[07:50:16]] [SUCCESS] Screenshot refreshed successfully
[[07:50:16]] [SUCCESS] Screenshot refreshed successfully
[[07:50:16]] [INFO] Executing Multi Step action step 37/41: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:50:15]] [SUCCESS] Screenshot refreshed
[[07:50:15]] [INFO] Refreshing screenshot...
[[07:50:11]] [SUCCESS] Screenshot refreshed successfully
[[07:50:11]] [SUCCESS] Screenshot refreshed successfully
[[07:50:11]] [INFO] Executing Multi Step action step 36/41: Tap on image: banner-close-updated.png
[[07:50:10]] [SUCCESS] Screenshot refreshed
[[07:50:10]] [INFO] Refreshing screenshot...
[[07:50:08]] [INFO] Executing Multi Step action step 35/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Sign in with your Zip account"]" exists
[[07:50:08]] [SUCCESS] Screenshot refreshed successfully
[[07:50:08]] [SUCCESS] Screenshot refreshed successfully
[[07:50:07]] [SUCCESS] Screenshot refreshed
[[07:50:07]] [INFO] Refreshing screenshot...
[[07:50:04]] [SUCCESS] Screenshot refreshed successfully
[[07:50:04]] [SUCCESS] Screenshot refreshed successfully
[[07:50:04]] [INFO] Executing Multi Step action step 34/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[07:50:03]] [SUCCESS] Screenshot refreshed
[[07:50:03]] [INFO] Refreshing screenshot...
[[07:50:00]] [SUCCESS] Screenshot refreshed successfully
[[07:50:00]] [SUCCESS] Screenshot refreshed successfully
[[07:50:00]] [INFO] Executing Multi Step action step 33/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther
[[07:49:59]] [SUCCESS] Screenshot refreshed
[[07:49:59]] [INFO] Refreshing screenshot...
[[07:49:55]] [SUCCESS] Screenshot refreshed successfully
[[07:49:55]] [SUCCESS] Screenshot refreshed successfully
[[07:49:55]] [INFO] Executing Multi Step action step 32/41: Tap on image: banner-close-updated.png
[[07:49:55]] [SUCCESS] Screenshot refreshed
[[07:49:55]] [INFO] Refreshing screenshot...
[[07:49:48]] [INFO] Executing Multi Step action step 31/41: Check if element with xpath="//XCUIElementTypeImage[@name="Afterpay"]" exists
[[07:49:48]] [SUCCESS] Screenshot refreshed successfully
[[07:49:48]] [SUCCESS] Screenshot refreshed successfully
[[07:49:48]] [SUCCESS] Screenshot refreshed
[[07:49:48]] [INFO] Refreshing screenshot...
[[07:49:44]] [SUCCESS] Screenshot refreshed successfully
[[07:49:44]] [SUCCESS] Screenshot refreshed successfully
[[07:49:44]] [INFO] Executing Multi Step action step 30/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Afterpay, available on orders between $70-$2,000."]
[[07:49:44]] [SUCCESS] Screenshot refreshed
[[07:49:44]] [INFO] Refreshing screenshot...
[[07:49:40]] [SUCCESS] Screenshot refreshed successfully
[[07:49:40]] [SUCCESS] Screenshot refreshed successfully
[[07:49:40]] [INFO] Executing Multi Step action step 29/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther
[[07:49:40]] [SUCCESS] Screenshot refreshed
[[07:49:40]] [INFO] Refreshing screenshot...
[[07:49:36]] [SUCCESS] Screenshot refreshed successfully
[[07:49:36]] [SUCCESS] Screenshot refreshed successfully
[[07:49:36]] [INFO] Executing Multi Step action step 28/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[07:49:35]] [SUCCESS] Screenshot refreshed
[[07:49:35]] [INFO] Refreshing screenshot...
[[07:49:32]] [SUCCESS] Screenshot refreshed successfully
[[07:49:32]] [SUCCESS] Screenshot refreshed successfully
[[07:49:32]] [INFO] Executing Multi Step action step 27/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay in 4 with PayPal"]" exists
[[07:49:32]] [SUCCESS] Screenshot refreshed
[[07:49:32]] [INFO] Refreshing screenshot...
[[07:49:28]] [SUCCESS] Screenshot refreshed successfully
[[07:49:28]] [SUCCESS] Screenshot refreshed successfully
[[07:49:28]] [INFO] Executing Multi Step action step 26/41: Tap on element with xpath: //XCUIElementTypeOther[@name="PayPal, main"]/XCUIElementTypeOther
[[07:49:28]] [SUCCESS] Screenshot refreshed
[[07:49:28]] [INFO] Refreshing screenshot...
[[07:49:24]] [SUCCESS] Screenshot refreshed successfully
[[07:49:24]] [SUCCESS] Screenshot refreshed successfully
[[07:49:24]] [INFO] Executing Multi Step action step 25/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther
[[07:49:24]] [SUCCESS] Screenshot refreshed
[[07:49:24]] [INFO] Refreshing screenshot...
[[07:49:20]] [SUCCESS] Screenshot refreshed successfully
[[07:49:20]] [SUCCESS] Screenshot refreshed successfully
[[07:49:20]] [INFO] Executing Multi Step action step 24/41: Tap on element with xpath: //XCUIElementTypeButton[@name="close"]
[[07:49:19]] [SUCCESS] Screenshot refreshed
[[07:49:19]] [INFO] Refreshing screenshot...
[[07:49:07]] [SUCCESS] Screenshot refreshed successfully
[[07:49:07]] [SUCCESS] Screenshot refreshed successfully
[[07:49:07]] [INFO] Executing Multi Step action step 23/41: Check if element with xpath="//XCUIElementTypeStaticText[@name="Pay with PayPal"]" exists
[[07:49:06]] [SUCCESS] Screenshot refreshed
[[07:49:06]] [INFO] Refreshing screenshot...
[[07:49:03]] [SUCCESS] Screenshot refreshed successfully
[[07:49:03]] [SUCCESS] Screenshot refreshed successfully
[[07:49:03]] [INFO] Executing Multi Step action step 22/41: Tap on element with xpath: //XCUIElementTypeLink[@name="PayPal"]
[[07:49:02]] [SUCCESS] Screenshot refreshed
[[07:49:02]] [INFO] Refreshing screenshot...
[[07:48:55]] [SUCCESS] Screenshot refreshed successfully
[[07:48:55]] [SUCCESS] Screenshot refreshed successfully
[[07:48:55]] [INFO] Executing Multi Step action step 21/41: swipeTillVisible action
[[07:48:54]] [SUCCESS] Screenshot refreshed
[[07:48:54]] [INFO] Refreshing screenshot...
[[07:48:51]] [SUCCESS] Screenshot refreshed successfully
[[07:48:51]] [SUCCESS] Screenshot refreshed successfully
[[07:48:51]] [INFO] Executing Multi Step action step 20/41: Tap on element with xpath: //XCUIElementTypeOther[@name="Select a payment method"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther
[[07:48:50]] [SUCCESS] Screenshot refreshed
[[07:48:50]] [INFO] Refreshing screenshot...
[[07:48:47]] [SUCCESS] Screenshot refreshed successfully
[[07:48:47]] [SUCCESS] Screenshot refreshed successfully
[[07:48:46]] [INFO] Executing Multi Step action step 19/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to payment"]
[[07:48:46]] [SUCCESS] Screenshot refreshed
[[07:48:46]] [INFO] Refreshing screenshot...
[[07:48:39]] [SUCCESS] Screenshot refreshed successfully
[[07:48:39]] [SUCCESS] Screenshot refreshed successfully
[[07:48:38]] [INFO] Executing Multi Step action step 18/41: swipeTillVisible action
[[07:48:38]] [SUCCESS] Screenshot refreshed
[[07:48:38]] [INFO] Refreshing screenshot...
[[07:48:34]] [SUCCESS] Screenshot refreshed successfully
[[07:48:34]] [SUCCESS] Screenshot refreshed successfully
[[07:48:34]] [INFO] Executing Multi Step action step 17/41: Tap on image: env[delivery-address-img]
[[07:48:33]] [SUCCESS] Screenshot refreshed
[[07:48:33]] [INFO] Refreshing screenshot...
[[07:48:30]] [SUCCESS] Screenshot refreshed successfully
[[07:48:30]] [SUCCESS] Screenshot refreshed successfully
[[07:48:30]] [INFO] Executing Multi Step action step 16/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[07:48:29]] [SUCCESS] Screenshot refreshed
[[07:48:29]] [INFO] Refreshing screenshot...
[[07:48:22]] [SUCCESS] Screenshot refreshed successfully
[[07:48:22]] [SUCCESS] Screenshot refreshed successfully
[[07:48:22]] [INFO] Executing Multi Step action step 15/41: Tap and Type at (env[delivery-addr-x], env[delivery-addr-y]): "env[deliver-address]"
[[07:48:22]] [SUCCESS] Screenshot refreshed
[[07:48:22]] [INFO] Refreshing screenshot...
[[07:48:16]] [SUCCESS] Screenshot refreshed successfully
[[07:48:16]] [SUCCESS] Screenshot refreshed successfully
[[07:48:16]] [INFO] Executing Multi Step action step 14/41: Tap on Text: "address"
[[07:48:16]] [SUCCESS] Screenshot refreshed
[[07:48:16]] [INFO] Refreshing screenshot...
[[07:48:12]] [SUCCESS] Screenshot refreshed successfully
[[07:48:12]] [SUCCESS] Screenshot refreshed successfully
[[07:48:12]] [INFO] Executing Multi Step action step 13/41: iOS Function: text
[[07:48:11]] [SUCCESS] Screenshot refreshed
[[07:48:11]] [INFO] Refreshing screenshot...
[[07:48:05]] [SUCCESS] Screenshot refreshed successfully
[[07:48:05]] [SUCCESS] Screenshot refreshed successfully
[[07:48:05]] [INFO] Executing Multi Step action step 12/41: textClear action
[[07:48:04]] [SUCCESS] Screenshot refreshed
[[07:48:04]] [INFO] Refreshing screenshot...
[[07:48:01]] [SUCCESS] Screenshot refreshed successfully
[[07:48:01]] [SUCCESS] Screenshot refreshed successfully
[[07:48:01]] [INFO] Executing Multi Step action step 11/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[07:48:00]] [SUCCESS] Screenshot refreshed
[[07:48:00]] [INFO] Refreshing screenshot...
[[07:47:54]] [SUCCESS] Screenshot refreshed successfully
[[07:47:54]] [SUCCESS] Screenshot refreshed successfully
[[07:47:54]] [INFO] Executing Multi Step action step 10/41: textClear action
[[07:47:53]] [SUCCESS] Screenshot refreshed
[[07:47:53]] [INFO] Refreshing screenshot...
[[07:47:50]] [SUCCESS] Screenshot refreshed successfully
[[07:47:50]] [SUCCESS] Screenshot refreshed successfully
[[07:47:50]] [INFO] Executing Multi Step action step 9/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:47:49]] [SUCCESS] Screenshot refreshed
[[07:47:49]] [INFO] Refreshing screenshot...
[[07:47:43]] [SUCCESS] Screenshot refreshed successfully
[[07:47:43]] [SUCCESS] Screenshot refreshed successfully
[[07:47:43]] [INFO] Executing Multi Step action step 8/41: textClear action
[[07:47:42]] [SUCCESS] Screenshot refreshed
[[07:47:42]] [INFO] Refreshing screenshot...
[[07:47:39]] [SUCCESS] Screenshot refreshed successfully
[[07:47:39]] [SUCCESS] Screenshot refreshed successfully
[[07:47:39]] [INFO] Executing Multi Step action step 7/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[07:47:38]] [SUCCESS] Screenshot refreshed
[[07:47:38]] [INFO] Refreshing screenshot...
[[07:47:32]] [SUCCESS] Screenshot refreshed successfully
[[07:47:32]] [SUCCESS] Screenshot refreshed successfully
[[07:47:32]] [INFO] Executing Multi Step action step 6/41: textClear action
[[07:47:31]] [SUCCESS] Screenshot refreshed
[[07:47:31]] [INFO] Refreshing screenshot...
[[07:47:28]] [SUCCESS] Screenshot refreshed successfully
[[07:47:28]] [SUCCESS] Screenshot refreshed successfully
[[07:47:28]] [INFO] Executing Multi Step action step 5/41: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[07:47:27]] [SUCCESS] Screenshot refreshed
[[07:47:27]] [INFO] Refreshing screenshot...
[[07:47:24]] [SUCCESS] Screenshot refreshed successfully
[[07:47:24]] [SUCCESS] Screenshot refreshed successfully
[[07:47:24]] [INFO] Executing Multi Step action step 4/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[07:47:23]] [SUCCESS] Screenshot refreshed
[[07:47:23]] [INFO] Refreshing screenshot...
[[07:47:05]] [SUCCESS] Screenshot refreshed successfully
[[07:47:05]] [SUCCESS] Screenshot refreshed successfully
[[07:47:05]] [INFO] Executing Multi Step action step 3/41: swipeTillVisible action
[[07:47:04]] [SUCCESS] Screenshot refreshed
[[07:47:04]] [INFO] Refreshing screenshot...
[[07:47:01]] [SUCCESS] Screenshot refreshed successfully
[[07:47:01]] [SUCCESS] Screenshot refreshed successfully
[[07:47:00]] [INFO] Executing Multi Step action step 2/41: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[07:47:00]] [SUCCESS] Screenshot refreshed
[[07:47:00]] [INFO] Refreshing screenshot...
[[07:46:55]] [INFO] Executing Multi Step action step 1/41: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:46:54]] [INFO] Loaded 41 steps from test case: Delivery Buy Steps
[[07:46:54]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps
[[07:46:54]] [INFO] Executing action 344/512: Execute Test Case: Delivery Buy Steps (41 steps)
[[07:46:54]] [SUCCESS] Screenshot refreshed successfully
[[07:46:54]] [SUCCESS] Screenshot refreshed successfully
[[07:46:54]] [SUCCESS] Screenshot refreshed
[[07:46:54]] [INFO] Refreshing screenshot...
[[07:46:51]] [SUCCESS] Screenshot refreshed successfully
[[07:46:51]] [SUCCESS] Screenshot refreshed successfully
[[07:46:50]] [INFO] Executing action 343/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:46:50]] [SUCCESS] Screenshot refreshed
[[07:46:50]] [INFO] Refreshing screenshot...
[[07:46:45]] [SUCCESS] Screenshot refreshed successfully
[[07:46:45]] [SUCCESS] Screenshot refreshed successfully
[[07:46:45]] [INFO] Executing action 342/512: Restart app: env[appid]
[[07:46:44]] [SUCCESS] Screenshot refreshed
[[07:46:44]] [INFO] Refreshing screenshot...
[[07:46:32]] [SUCCESS] Screenshot refreshed successfully
[[07:46:32]] [SUCCESS] Screenshot refreshed successfully
[[07:46:32]] [INFO] Executing action 341/512: If exists: xpath="//XCUIElementTypeButton[@name="Save my location"]" (timeout: 10s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="Save my location"]
[[07:46:31]] [SUCCESS] Screenshot refreshed
[[07:46:31]] [INFO] Refreshing screenshot...
[[07:46:28]] [SUCCESS] Screenshot refreshed successfully
[[07:46:28]] [SUCCESS] Screenshot refreshed successfully
[[07:46:27]] [INFO] Executing action 340/512: Tap on image: env[atg-pdp]
[[07:46:27]] [SUCCESS] Screenshot refreshed
[[07:46:27]] [INFO] Refreshing screenshot...
[[07:46:23]] [SUCCESS] Screenshot refreshed successfully
[[07:46:23]] [SUCCESS] Screenshot refreshed successfully
[[07:46:23]] [INFO] Executing action 339/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:46:23]] [SUCCESS] Screenshot refreshed
[[07:46:23]] [INFO] Refreshing screenshot...
[[07:46:20]] [SUCCESS] Screenshot refreshed successfully
[[07:46:20]] [SUCCESS] Screenshot refreshed successfully
[[07:46:20]] [INFO] Executing action 338/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:46:19]] [SUCCESS] Screenshot refreshed
[[07:46:19]] [INFO] Refreshing screenshot...
[[07:46:16]] [SUCCESS] Screenshot refreshed successfully
[[07:46:16]] [SUCCESS] Screenshot refreshed successfully
[[07:46:15]] [INFO] Executing action 337/512: iOS Function: text
[[07:46:15]] [SUCCESS] Screenshot refreshed
[[07:46:15]] [INFO] Refreshing screenshot...
[[07:46:10]] [SUCCESS] Screenshot refreshed successfully
[[07:46:10]] [SUCCESS] Screenshot refreshed successfully
[[07:46:09]] [INFO] Executing action 336/512: Tap on Text: "Find"
[[07:46:09]] [SUCCESS] Screenshot refreshed
[[07:46:09]] [INFO] Refreshing screenshot...
[[07:46:05]] [SUCCESS] Screenshot refreshed successfully
[[07:46:05]] [SUCCESS] Screenshot refreshed successfully
[[07:46:04]] [INFO] Executing action 335/512: Tap on image: env[device-back-img]
[[07:46:04]] [SUCCESS] Screenshot refreshed
[[07:46:04]] [INFO] Refreshing screenshot...
[[07:45:41]] [SUCCESS] Screenshot refreshed successfully
[[07:45:41]] [SUCCESS] Screenshot refreshed successfully
[[07:45:41]] [INFO] Executing action 334/512: If exists: xpath="//XCUIElementTypeButton[@name="btnUpdate"]" (timeout: 20s) → Then tap on element with xpath: //XCUIElementTypeButton[@name="btnUpdate"]
[[07:45:41]] [SUCCESS] Screenshot refreshed
[[07:45:41]] [INFO] Refreshing screenshot...
[[07:45:36]] [SUCCESS] Screenshot refreshed successfully
[[07:45:36]] [SUCCESS] Screenshot refreshed successfully
[[07:45:36]] [INFO] Executing action 333/512: Tap on Text: "current"
[[07:45:36]] [SUCCESS] Screenshot refreshed
[[07:45:36]] [INFO] Refreshing screenshot...
[[07:45:31]] [SUCCESS] Screenshot refreshed successfully
[[07:45:31]] [SUCCESS] Screenshot refreshed successfully
[[07:45:31]] [INFO] Executing action 332/512: Tap on Text: "Edit"
[[07:45:30]] [SUCCESS] Screenshot refreshed
[[07:45:30]] [INFO] Refreshing screenshot...
[[07:45:25]] [SUCCESS] Screenshot refreshed successfully
[[07:45:25]] [SUCCESS] Screenshot refreshed successfully
[[07:45:25]] [INFO] Executing action 331/512: Restart app: env[appid]
[[07:45:24]] [SUCCESS] Screenshot refreshed
[[07:45:24]] [INFO] Refreshing screenshot...
[[07:45:20]] [SUCCESS] Screenshot refreshed successfully
[[07:45:20]] [SUCCESS] Screenshot refreshed successfully
[[07:45:20]] [INFO] Executing action 330/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[07:45:19]] [SUCCESS] Screenshot refreshed
[[07:45:19]] [INFO] Refreshing screenshot...
[[07:45:16]] [SUCCESS] Screenshot refreshed successfully
[[07:45:16]] [SUCCESS] Screenshot refreshed successfully
[[07:45:16]] [INFO] Executing action 329/512: Check if element with xpath="//XCUIElementTypeOther[@name="Slyp Receipt"]/XCUIElementTypeOther[2]" exists
[[07:45:16]] [SUCCESS] Screenshot refreshed
[[07:45:16]] [INFO] Refreshing screenshot...
[[07:45:12]] [SUCCESS] Screenshot refreshed successfully
[[07:45:12]] [SUCCESS] Screenshot refreshed successfully
[[07:45:12]] [INFO] Executing action 328/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]
[[07:45:11]] [SUCCESS] Screenshot refreshed
[[07:45:11]] [INFO] Refreshing screenshot...
[[07:45:09]] [SUCCESS] Screenshot refreshed successfully
[[07:45:09]] [SUCCESS] Screenshot refreshed successfully
[[07:45:08]] [INFO] Executing action 327/512: Check if element with xpath="(//XCUIElementTypeOther[contains(@name,"Store receipts")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]" exists
[[07:45:08]] [SUCCESS] Screenshot refreshed
[[07:45:08]] [INFO] Refreshing screenshot...
[[07:45:03]] [SUCCESS] Screenshot refreshed successfully
[[07:45:03]] [SUCCESS] Screenshot refreshed successfully
[[07:45:03]] [INFO] Executing action 326/512: Tap on Text: "Store"
[[07:45:03]] [SUCCESS] Screenshot refreshed
[[07:45:03]] [INFO] Refreshing screenshot...
[[07:44:59]] [SUCCESS] Screenshot refreshed successfully
[[07:44:59]] [SUCCESS] Screenshot refreshed successfully
[[07:44:59]] [INFO] Executing action 325/512: Tap on Text: "receipts"
[[07:44:58]] [SUCCESS] Screenshot refreshed
[[07:44:58]] [INFO] Refreshing screenshot...
[[07:44:55]] [SUCCESS] Screenshot refreshed successfully
[[07:44:55]] [SUCCESS] Screenshot refreshed successfully
[[07:44:54]] [INFO] Executing action 324/512: Tap on image: env[device-back-img]
[[07:44:54]] [SUCCESS] Screenshot refreshed
[[07:44:54]] [INFO] Refreshing screenshot...
[[07:44:51]] [SUCCESS] Screenshot refreshed successfully
[[07:44:51]] [SUCCESS] Screenshot refreshed successfully
[[07:44:51]] [INFO] Executing action 323/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtOnePassSubscritionBox"]" exists
[[07:44:50]] [SUCCESS] Screenshot refreshed
[[07:44:50]] [INFO] Refreshing screenshot...
[[07:44:47]] [SUCCESS] Screenshot refreshed successfully
[[07:44:47]] [SUCCESS] Screenshot refreshed successfully
[[07:44:47]] [INFO] Executing action 322/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMy OnePass Account"]
[[07:44:46]] [SUCCESS] Screenshot refreshed
[[07:44:46]] [INFO] Refreshing screenshot...
[[07:44:43]] [SUCCESS] Screenshot refreshed successfully
[[07:44:43]] [SUCCESS] Screenshot refreshed successfully
[[07:44:42]] [INFO] Executing action 321/512: Check if element with xpath="//XCUIElementTypeButton[@name="txtMy OnePass Account"]" exists
[[07:44:42]] [SUCCESS] Screenshot refreshed
[[07:44:42]] [INFO] Refreshing screenshot...
[[07:44:38]] [SUCCESS] Screenshot refreshed successfully
[[07:44:38]] [SUCCESS] Screenshot refreshed successfully
[[07:44:38]] [INFO] Executing action 320/512: iOS Function: text
[[07:44:37]] [SUCCESS] Screenshot refreshed
[[07:44:37]] [INFO] Refreshing screenshot...
[[07:44:34]] [SUCCESS] Screenshot refreshed successfully
[[07:44:34]] [SUCCESS] Screenshot refreshed successfully
[[07:44:33]] [INFO] Executing action 319/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:44:33]] [SUCCESS] Screenshot refreshed
[[07:44:33]] [INFO] Refreshing screenshot...
[[07:44:29]] [SUCCESS] Screenshot refreshed successfully
[[07:44:29]] [SUCCESS] Screenshot refreshed successfully
[[07:44:29]] [INFO] Executing action 318/512: iOS Function: text
[[07:44:28]] [SUCCESS] Screenshot refreshed
[[07:44:28]] [INFO] Refreshing screenshot...
[[07:44:24]] [SUCCESS] Screenshot refreshed successfully
[[07:44:24]] [SUCCESS] Screenshot refreshed successfully
[[07:44:24]] [INFO] Executing action 317/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:44:24]] [SUCCESS] Screenshot refreshed
[[07:44:24]] [INFO] Refreshing screenshot...
[[07:44:21]] [SUCCESS] Screenshot refreshed successfully
[[07:44:21]] [SUCCESS] Screenshot refreshed successfully
[[07:44:21]] [INFO] Executing action 316/512: iOS Function: alert_accept
[[07:44:20]] [SUCCESS] Screenshot refreshed
[[07:44:20]] [INFO] Refreshing screenshot...
[[07:44:17]] [SUCCESS] Screenshot refreshed successfully
[[07:44:17]] [SUCCESS] Screenshot refreshed successfully
[[07:44:17]] [INFO] Executing action 315/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtMoreAccountCtaSignIn"]
[[07:44:16]] [SUCCESS] Screenshot refreshed
[[07:44:16]] [INFO] Refreshing screenshot...
[[07:44:13]] [SUCCESS] Screenshot refreshed successfully
[[07:44:13]] [SUCCESS] Screenshot refreshed successfully
[[07:44:13]] [INFO] Executing action 314/512: Tap on image: env[device-back-img]
[[07:44:12]] [SUCCESS] Screenshot refreshed
[[07:44:12]] [INFO] Refreshing screenshot...
[[07:44:10]] [SUCCESS] Screenshot refreshed successfully
[[07:44:10]] [SUCCESS] Screenshot refreshed successfully
[[07:44:09]] [INFO] Executing action 313/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="refunded"]" exists
[[07:44:09]] [SUCCESS] Screenshot refreshed
[[07:44:09]] [INFO] Refreshing screenshot...
[[07:44:05]] [SUCCESS] Screenshot refreshed successfully
[[07:44:05]] [SUCCESS] Screenshot refreshed successfully
[[07:44:05]] [INFO] Executing action 312/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Search for order"]
[[07:44:05]] [SUCCESS] Screenshot refreshed
[[07:44:05]] [INFO] Refreshing screenshot...
[[07:44:02]] [SUCCESS] Screenshot refreshed successfully
[[07:44:02]] [SUCCESS] Screenshot refreshed successfully
[[07:44:02]] [INFO] Executing action 311/512: Input text: "env[uname-op]"
[[07:44:01]] [SUCCESS] Screenshot refreshed
[[07:44:01]] [INFO] Refreshing screenshot...
[[07:43:58]] [SUCCESS] Screenshot refreshed successfully
[[07:43:58]] [SUCCESS] Screenshot refreshed successfully
[[07:43:58]] [INFO] Executing action 310/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email Address"] with fallback: Coordinates (98, 308)
[[07:43:57]] [SUCCESS] Screenshot refreshed
[[07:43:57]] [INFO] Refreshing screenshot...
[[07:43:54]] [SUCCESS] Screenshot refreshed successfully
[[07:43:54]] [SUCCESS] Screenshot refreshed successfully
[[07:43:54]] [INFO] Executing action 309/512: Input text: "env[searchorder]"
[[07:43:54]] [SUCCESS] Screenshot refreshed
[[07:43:54]] [INFO] Refreshing screenshot...
[[07:43:50]] [SUCCESS] Screenshot refreshed successfully
[[07:43:50]] [SUCCESS] Screenshot refreshed successfully
[[07:43:50]] [INFO] Executing action 308/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Order number"]
[[07:43:50]] [SUCCESS] Screenshot refreshed
[[07:43:50]] [INFO] Refreshing screenshot...
[[07:43:47]] [SUCCESS] Screenshot refreshed successfully
[[07:43:47]] [SUCCESS] Screenshot refreshed successfully
[[07:43:46]] [INFO] Executing action 307/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtTrack My Order"]
[[07:43:46]] [SUCCESS] Screenshot refreshed
[[07:43:46]] [INFO] Refreshing screenshot...
[[07:43:43]] [SUCCESS] Screenshot refreshed successfully
[[07:43:43]] [SUCCESS] Screenshot refreshed successfully
[[07:43:42]] [INFO] Executing action 306/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:43:42]] [SUCCESS] Screenshot refreshed
[[07:43:42]] [INFO] Refreshing screenshot...
[[07:43:39]] [SUCCESS] Screenshot refreshed successfully
[[07:43:39]] [SUCCESS] Screenshot refreshed successfully
[[07:43:37]] [INFO] Executing action 305/512: Tap on image: env[device-back-img]
[[07:43:37]] [SUCCESS] Screenshot refreshed
[[07:43:37]] [INFO] Refreshing screenshot...
[[07:43:34]] [SUCCESS] Screenshot refreshed successfully
[[07:43:34]] [SUCCESS] Screenshot refreshed successfully
[[07:43:33]] [INFO] Executing action 304/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPosition barcode lengthwise within rectangle frame to view helpful product information"]" exists
[[07:43:32]] [SUCCESS] Screenshot refreshed
[[07:43:32]] [INFO] Refreshing screenshot...
[[07:43:30]] [SUCCESS] Screenshot refreshed successfully
[[07:43:30]] [SUCCESS] Screenshot refreshed successfully
[[07:43:28]] [INFO] Executing action 303/512: Check if element with xpath="//XCUIElementTypeButton[@name="imgHelp"]" exists
[[07:43:28]] [SUCCESS] Screenshot refreshed
[[07:43:28]] [INFO] Refreshing screenshot...
[[07:43:25]] [SUCCESS] Screenshot refreshed successfully
[[07:43:25]] [SUCCESS] Screenshot refreshed successfully
[[07:43:24]] [INFO] Executing action 302/512: Check if element with xpath="//XCUIElementTypeOther[@name="Barcode Scanner"]" exists
[[07:43:23]] [SUCCESS] Screenshot refreshed
[[07:43:23]] [INFO] Refreshing screenshot...
[[07:43:21]] [SUCCESS] Screenshot refreshed successfully
[[07:43:21]] [SUCCESS] Screenshot refreshed successfully
[[07:43:20]] [INFO] Executing action 301/512: iOS Function: alert_accept
[[07:43:19]] [SUCCESS] Screenshot refreshed
[[07:43:19]] [INFO] Refreshing screenshot...
[[07:43:15]] [SUCCESS] Screenshot refreshed successfully
[[07:43:15]] [SUCCESS] Screenshot refreshed successfully
[[07:43:15]] [INFO] Executing action 300/512: Tap on element with xpath: //XCUIElementTypeButton[@name="btnBarcodeScanner"]
[[07:43:14]] [SUCCESS] Screenshot refreshed
[[07:43:14]] [INFO] Refreshing screenshot...
[[07:42:59]] [SUCCESS] Screenshot refreshed successfully
[[07:42:59]] [SUCCESS] Screenshot refreshed successfully
[[07:42:59]] [INFO] Executing action 299/512: Restart app: env[appid]
[[07:42:58]] [SUCCESS] Screenshot refreshed
[[07:42:58]] [INFO] Refreshing screenshot...
[[07:42:54]] [SUCCESS] Screenshot refreshed successfully
[[07:42:54]] [SUCCESS] Screenshot refreshed successfully
[[07:42:54]] [INFO] Executing action 298/512: Tap on Text: "out"
[[07:42:53]] [SUCCESS] Screenshot refreshed
[[07:42:53]] [INFO] Refreshing screenshot...
[[07:42:47]] [SUCCESS] Screenshot refreshed successfully
[[07:42:47]] [SUCCESS] Screenshot refreshed successfully
[[07:42:47]] [INFO] Executing action 297/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:42:46]] [SUCCESS] Screenshot refreshed
[[07:42:46]] [INFO] Refreshing screenshot...
[[07:42:42]] [SUCCESS] Screenshot refreshed successfully
[[07:42:42]] [SUCCESS] Screenshot refreshed successfully
[[07:42:42]] [INFO] Executing action 296/512: Tap on image: env[device-back-img]
[[07:42:42]] [SUCCESS] Screenshot refreshed
[[07:42:42]] [INFO] Refreshing screenshot...
[[07:42:38]] [SUCCESS] Screenshot refreshed successfully
[[07:42:38]] [SUCCESS] Screenshot refreshed successfully
[[07:42:38]] [INFO] Executing action 295/512: Tap on Text: "Customer"
[[07:42:37]] [SUCCESS] Screenshot refreshed
[[07:42:37]] [INFO] Refreshing screenshot...
[[07:42:33]] [SUCCESS] Screenshot refreshed successfully
[[07:42:33]] [SUCCESS] Screenshot refreshed successfully
[[07:42:33]] [INFO] Executing action 294/512: Tap on image: banner-close-updated.png
[[07:42:32]] [SUCCESS] Screenshot refreshed
[[07:42:32]] [INFO] Refreshing screenshot...
[[07:42:29]] [SUCCESS] Screenshot refreshed successfully
[[07:42:29]] [SUCCESS] Screenshot refreshed successfully
[[07:42:29]] [INFO] Executing action 293/512: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"I’m loving the new Kmart app")]" exists
[[07:42:29]] [SUCCESS] Screenshot refreshed
[[07:42:29]] [INFO] Refreshing screenshot...
[[07:42:24]] [SUCCESS] Screenshot refreshed successfully
[[07:42:24]] [SUCCESS] Screenshot refreshed successfully
[[07:42:24]] [INFO] Executing action 292/512: Tap on Text: "Invite"
[[07:42:23]] [SUCCESS] Screenshot refreshed
[[07:42:23]] [INFO] Refreshing screenshot...
[[07:42:19]] [SUCCESS] Screenshot refreshed successfully
[[07:42:19]] [SUCCESS] Screenshot refreshed successfully
[[07:42:19]] [INFO] Executing action 291/512: Tap on image: env[device-back-img]
[[07:42:19]] [SUCCESS] Screenshot refreshed
[[07:42:19]] [INFO] Refreshing screenshot...
[[07:42:16]] [SUCCESS] Screenshot refreshed successfully
[[07:42:16]] [SUCCESS] Screenshot refreshed successfully
[[07:42:16]] [INFO] Executing action 290/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Melbourne Cbd"]" exists
[[07:42:15]] [SUCCESS] Screenshot refreshed
[[07:42:15]] [INFO] Refreshing screenshot...
[[07:42:11]] [SUCCESS] Screenshot refreshed successfully
[[07:42:11]] [SUCCESS] Screenshot refreshed successfully
[[07:42:11]] [INFO] Executing action 289/512: Tap on Text: "VIC"
[[07:42:10]] [SUCCESS] Screenshot refreshed
[[07:42:10]] [INFO] Refreshing screenshot...
[[07:42:04]] [SUCCESS] Screenshot refreshed successfully
[[07:42:04]] [SUCCESS] Screenshot refreshed successfully
[[07:42:04]] [INFO] Executing action 288/512: Tap and Type at (env[store-locator-x], env[store-locator-y]): "env[store-locator-postcode]"
[[07:42:03]] [SUCCESS] Screenshot refreshed
[[07:42:03]] [INFO] Refreshing screenshot...
[[07:41:58]] [SUCCESS] Screenshot refreshed successfully
[[07:41:58]] [SUCCESS] Screenshot refreshed successfully
[[07:41:58]] [INFO] Executing action 287/512: Tap on Text: "Nearby"
[[07:41:58]] [SUCCESS] Screenshot refreshed
[[07:41:58]] [INFO] Refreshing screenshot...
[[07:41:54]] [SUCCESS] Screenshot refreshed successfully
[[07:41:54]] [SUCCESS] Screenshot refreshed successfully
[[07:41:54]] [INFO] Executing action 286/512: Tap on Text: "locator"
[[07:41:53]] [SUCCESS] Screenshot refreshed
[[07:41:53]] [INFO] Refreshing screenshot...
[[07:41:47]] [SUCCESS] Screenshot refreshed successfully
[[07:41:47]] [SUCCESS] Screenshot refreshed successfully
[[07:41:47]] [INFO] Executing action 285/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:41:46]] [SUCCESS] Screenshot refreshed
[[07:41:46]] [INFO] Refreshing screenshot...
[[07:41:43]] [SUCCESS] Screenshot refreshed successfully
[[07:41:43]] [SUCCESS] Screenshot refreshed successfully
[[07:41:43]] [INFO] Executing action 284/512: Check if element with accessibility_id="txtMy Flybuys card" exists
[[07:41:42]] [SUCCESS] Screenshot refreshed
[[07:41:42]] [INFO] Refreshing screenshot...
[[07:41:39]] [SUCCESS] Screenshot refreshed successfully
[[07:41:39]] [SUCCESS] Screenshot refreshed successfully
[[07:41:39]] [INFO] Executing action 283/512: Tap on image: env[device-back-img]
[[07:41:38]] [SUCCESS] Screenshot refreshed
[[07:41:38]] [INFO] Refreshing screenshot...
[[07:41:33]] [SUCCESS] Screenshot refreshed successfully
[[07:41:33]] [SUCCESS] Screenshot refreshed successfully
[[07:41:33]] [INFO] Executing action 282/512: Tap on element with accessibility_id: btnSaveFlybuysCard
[[07:41:33]] [SUCCESS] Screenshot refreshed
[[07:41:33]] [INFO] Refreshing screenshot...
[[07:41:28]] [SUCCESS] Screenshot refreshed successfully
[[07:41:28]] [SUCCESS] Screenshot refreshed successfully
[[07:41:28]] [INFO] Executing action 281/512: Tap on element with accessibility_id: Done
[[07:41:27]] [SUCCESS] Screenshot refreshed
[[07:41:27]] [INFO] Refreshing screenshot...
[[07:41:25]] [SUCCESS] Screenshot refreshed successfully
[[07:41:25]] [SUCCESS] Screenshot refreshed successfully
[[07:41:24]] [INFO] Executing action 280/512: Input text: "2791234567890"
[[07:41:24]] [SUCCESS] Screenshot refreshed
[[07:41:24]] [INFO] Refreshing screenshot...
[[07:41:19]] [SUCCESS] Screenshot refreshed successfully
[[07:41:19]] [SUCCESS] Screenshot refreshed successfully
[[07:41:19]] [INFO] Executing action 279/512: Tap on element with accessibility_id: Flybuys barcode number
[[07:41:19]] [SUCCESS] Screenshot refreshed
[[07:41:19]] [INFO] Refreshing screenshot...
[[07:41:14]] [SUCCESS] Screenshot refreshed successfully
[[07:41:14]] [SUCCESS] Screenshot refreshed successfully
[[07:41:14]] [INFO] Executing action 278/512: Tap on element with accessibility_id: btnLinkFlyBuys
[[07:41:14]] [SUCCESS] Screenshot refreshed
[[07:41:14]] [INFO] Refreshing screenshot...
[[07:41:09]] [SUCCESS] Screenshot refreshed successfully
[[07:41:09]] [SUCCESS] Screenshot refreshed successfully
[[07:41:09]] [INFO] Executing action 277/512: Tap on Text: "Flybuys"
[[07:41:08]] [SUCCESS] Screenshot refreshed
[[07:41:08]] [INFO] Refreshing screenshot...
[[07:41:04]] [SUCCESS] Screenshot refreshed successfully
[[07:41:04]] [SUCCESS] Screenshot refreshed successfully
[[07:41:04]] [INFO] Executing action 276/512: Tap on element with accessibility_id: btnRemove
[[07:41:03]] [SUCCESS] Screenshot refreshed
[[07:41:03]] [INFO] Refreshing screenshot...
[[07:40:59]] [SUCCESS] Screenshot refreshed successfully
[[07:40:59]] [SUCCESS] Screenshot refreshed successfully
[[07:40:58]] [INFO] Executing action 275/512: Tap on element with accessibility_id: Remove card
[[07:40:58]] [SUCCESS] Screenshot refreshed
[[07:40:58]] [INFO] Refreshing screenshot...
[[07:40:53]] [SUCCESS] Screenshot refreshed successfully
[[07:40:53]] [SUCCESS] Screenshot refreshed successfully
[[07:40:53]] [INFO] Executing action 274/512: Tap on element with accessibility_id: btneditFlybuysCard
[[07:40:52]] [SUCCESS] Screenshot refreshed
[[07:40:52]] [INFO] Refreshing screenshot...
[[07:40:49]] [SUCCESS] Screenshot refreshed successfully
[[07:40:49]] [SUCCESS] Screenshot refreshed successfully
[[07:40:48]] [INFO] Executing action 273/512: Wait till accessibility_id=btneditFlybuysCard
[[07:40:48]] [SUCCESS] Screenshot refreshed
[[07:40:48]] [INFO] Refreshing screenshot...
[[07:40:44]] [SUCCESS] Screenshot refreshed successfully
[[07:40:44]] [SUCCESS] Screenshot refreshed successfully
[[07:40:44]] [INFO] Executing action 272/512: Tap on Text: "Flybuys"
[[07:40:43]] [SUCCESS] Screenshot refreshed
[[07:40:43]] [INFO] Refreshing screenshot...
[[07:40:39]] [SUCCESS] Screenshot refreshed successfully
[[07:40:39]] [SUCCESS] Screenshot refreshed successfully
[[07:40:39]] [INFO] Executing action 271/512: Tap on image: env[device-back-img]
[[07:40:39]] [SUCCESS] Screenshot refreshed
[[07:40:39]] [INFO] Refreshing screenshot...
[[07:40:35]] [SUCCESS] Screenshot refreshed successfully
[[07:40:35]] [SUCCESS] Screenshot refreshed successfully
[[07:40:35]] [INFO] Executing action 270/512: Tap on image: env[device-back-img]
[[07:40:35]] [SUCCESS] Screenshot refreshed
[[07:40:35]] [INFO] Refreshing screenshot...
[[07:40:30]] [SUCCESS] Screenshot refreshed successfully
[[07:40:30]] [SUCCESS] Screenshot refreshed successfully
[[07:40:30]] [INFO] Executing action 269/512: Tap on Text: "payment"
[[07:40:30]] [SUCCESS] Screenshot refreshed
[[07:40:30]] [INFO] Refreshing screenshot...
[[07:40:26]] [SUCCESS] Screenshot refreshed successfully
[[07:40:26]] [SUCCESS] Screenshot refreshed successfully
[[07:40:26]] [INFO] Executing action 268/512: Tap on image: env[device-back-img]
[[07:40:26]] [SUCCESS] Screenshot refreshed
[[07:40:26]] [INFO] Refreshing screenshot...
[[07:40:21]] [SUCCESS] Screenshot refreshed successfully
[[07:40:21]] [SUCCESS] Screenshot refreshed successfully
[[07:40:21]] [INFO] Executing action 267/512: Tap on Text: "address"
[[07:40:21]] [SUCCESS] Screenshot refreshed
[[07:40:21]] [INFO] Refreshing screenshot...
[[07:40:17]] [SUCCESS] Screenshot refreshed successfully
[[07:40:17]] [SUCCESS] Screenshot refreshed successfully
[[07:40:17]] [INFO] Executing action 266/512: Tap on image: env[device-back-img]
[[07:40:17]] [SUCCESS] Screenshot refreshed
[[07:40:17]] [INFO] Refreshing screenshot...
[[07:40:12]] [SUCCESS] Screenshot refreshed successfully
[[07:40:12]] [SUCCESS] Screenshot refreshed successfully
[[07:40:12]] [INFO] Executing action 265/512: Tap on Text: "details"
[[07:40:12]] [SUCCESS] Screenshot refreshed
[[07:40:12]] [INFO] Refreshing screenshot...
[[07:40:08]] [SUCCESS] Screenshot refreshed successfully
[[07:40:08]] [SUCCESS] Screenshot refreshed successfully
[[07:40:08]] [INFO] Executing action 264/512: Tap on element with xpath: //XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[07:40:08]] [SUCCESS] Screenshot refreshed
[[07:40:08]] [INFO] Refreshing screenshot...
[[07:40:05]] [SUCCESS] Screenshot refreshed successfully
[[07:40:05]] [SUCCESS] Screenshot refreshed successfully
[[07:40:04]] [INFO] Executing action 263/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:40:04]] [SUCCESS] Screenshot refreshed
[[07:40:04]] [INFO] Refreshing screenshot...
[[07:40:00]] [SUCCESS] Screenshot refreshed successfully
[[07:40:00]] [SUCCESS] Screenshot refreshed successfully
[[07:40:00]] [INFO] Executing action 262/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:39:59]] [SUCCESS] Screenshot refreshed
[[07:39:59]] [INFO] Refreshing screenshot...
[[07:39:55]] [SUCCESS] Screenshot refreshed successfully
[[07:39:55]] [SUCCESS] Screenshot refreshed successfully
[[07:39:55]] [INFO] Executing action 261/512: Tap on Text: "Return"
[[07:39:52]] [SUCCESS] Screenshot refreshed
[[07:39:52]] [INFO] Refreshing screenshot...
[[07:39:46]] [SUCCESS] Screenshot refreshed successfully
[[07:39:46]] [SUCCESS] Screenshot refreshed successfully
[[07:39:46]] [INFO] Executing action 260/512: Wait for 5 ms
[[07:39:45]] [SUCCESS] Screenshot refreshed
[[07:39:45]] [INFO] Refreshing screenshot...
[[07:39:33]] [SUCCESS] Screenshot refreshed successfully
[[07:39:33]] [SUCCESS] Screenshot refreshed successfully
[[07:39:33]] [INFO] Executing action 259/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:39:33]] [SUCCESS] Screenshot refreshed
[[07:39:33]] [INFO] Refreshing screenshot...
[[07:39:29]] [SUCCESS] Screenshot refreshed successfully
[[07:39:29]] [SUCCESS] Screenshot refreshed successfully
[[07:39:29]] [INFO] Executing action 258/512: Tap on image: env[device-back-img]
[[07:39:28]] [SUCCESS] Screenshot refreshed
[[07:39:28]] [INFO] Refreshing screenshot...
[[07:39:26]] [SUCCESS] Screenshot refreshed successfully
[[07:39:26]] [SUCCESS] Screenshot refreshed successfully
[[07:39:26]] [INFO] Executing action 257/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Exchanges Returns"]" exists
[[07:39:25]] [SUCCESS] Screenshot refreshed
[[07:39:25]] [INFO] Refreshing screenshot...
[[07:39:22]] [SUCCESS] Screenshot refreshed successfully
[[07:39:22]] [SUCCESS] Screenshot refreshed successfully
[[07:39:22]] [INFO] Executing action 256/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Learn more about refunds"]
[[07:39:21]] [SUCCESS] Screenshot refreshed
[[07:39:21]] [INFO] Refreshing screenshot...
[[07:39:18]] [SUCCESS] Screenshot refreshed successfully
[[07:39:18]] [SUCCESS] Screenshot refreshed successfully
[[07:39:18]] [INFO] Executing action 255/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Learn more about refunds"]" exists
[[07:39:18]] [SUCCESS] Screenshot refreshed
[[07:39:18]] [INFO] Refreshing screenshot...
[[07:39:14]] [SUCCESS] Screenshot refreshed successfully
[[07:39:14]] [SUCCESS] Screenshot refreshed successfully
[[07:39:14]] [INFO] Executing action 254/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[07:39:14]] [SUCCESS] Screenshot refreshed
[[07:39:14]] [INFO] Refreshing screenshot...
[[07:39:07]] [SUCCESS] Screenshot refreshed successfully
[[07:39:07]] [SUCCESS] Screenshot refreshed successfully
[[07:39:07]] [INFO] Executing action 253/512: Wait for 5 ms
[[07:39:07]] [SUCCESS] Screenshot refreshed
[[07:39:07]] [INFO] Refreshing screenshot...
[[07:39:03]] [SUCCESS] Screenshot refreshed successfully
[[07:39:03]] [SUCCESS] Screenshot refreshed successfully
[[07:39:03]] [INFO] Executing action 252/512: Tap on image: env[device-back-img]
[[07:39:02]] [SUCCESS] Screenshot refreshed
[[07:39:02]] [INFO] Refreshing screenshot...
[[07:38:58]] [INFO] Executing action 251/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Cancel"]
[[07:38:58]] [SUCCESS] Screenshot refreshed successfully
[[07:38:58]] [SUCCESS] Screenshot refreshed successfully
[[07:38:58]] [SUCCESS] Screenshot refreshed
[[07:38:58]] [INFO] Refreshing screenshot...
[[07:38:53]] [SUCCESS] Screenshot refreshed successfully
[[07:38:53]] [SUCCESS] Screenshot refreshed successfully
[[07:38:53]] [INFO] Executing action 250/512: Tap on element with accessibility_id: Print order details
[[07:38:52]] [SUCCESS] Screenshot refreshed
[[07:38:52]] [INFO] Refreshing screenshot...
[[07:38:49]] [SUCCESS] Screenshot refreshed successfully
[[07:38:49]] [SUCCESS] Screenshot refreshed successfully
[[07:38:49]] [INFO] Executing action 249/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Email tax invoice"]
[[07:38:48]] [SUCCESS] Screenshot refreshed
[[07:38:48]] [INFO] Refreshing screenshot...
[[07:38:34]] [SUCCESS] Screenshot refreshed successfully
[[07:38:34]] [SUCCESS] Screenshot refreshed successfully
[[07:38:34]] [INFO] Executing action 248/512: swipeTillVisible action
[[07:38:33]] [SUCCESS] Screenshot refreshed
[[07:38:33]] [INFO] Refreshing screenshot...
[[07:38:30]] [SUCCESS] Screenshot refreshed successfully
[[07:38:30]] [SUCCESS] Screenshot refreshed successfully
[[07:38:30]] [INFO] Executing action 247/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"Online orders")]//XCUIElementTypeLink)[1]
[[07:38:29]] [SUCCESS] Screenshot refreshed
[[07:38:29]] [INFO] Refreshing screenshot...
[[07:38:23]] [SUCCESS] Screenshot refreshed successfully
[[07:38:23]] [SUCCESS] Screenshot refreshed successfully
[[07:38:22]] [INFO] Executing action 246/512: Wait for 5 ms
[[07:38:22]] [SUCCESS] Screenshot refreshed
[[07:38:22]] [INFO] Refreshing screenshot...
[[07:38:18]] [SUCCESS] Screenshot refreshed successfully
[[07:38:18]] [SUCCESS] Screenshot refreshed successfully
[[07:38:18]] [INFO] Executing action 245/512: Tap on Text: "receipts"
[[07:38:17]] [SUCCESS] Screenshot refreshed
[[07:38:17]] [INFO] Refreshing screenshot...
[[07:38:14]] [SUCCESS] Screenshot refreshed successfully
[[07:38:14]] [SUCCESS] Screenshot refreshed successfully
[[07:38:14]] [INFO] Executing action 244/512: Wait till xpath=//XCUIElementTypeStaticText[contains(@name,"Manage your account")]
[[07:38:14]] [SUCCESS] Screenshot refreshed
[[07:38:14]] [INFO] Refreshing screenshot...
[[07:38:12]] [SUCCESS] Screenshot refreshed successfully
[[07:38:12]] [SUCCESS] Screenshot refreshed successfully
[[07:38:10]] [INFO] Executing action 243/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:38:10]] [SUCCESS] Screenshot refreshed
[[07:38:10]] [INFO] Refreshing screenshot...
[[07:38:09]] [SUCCESS] Screenshot refreshed
[[07:38:09]] [INFO] Refreshing screenshot...
[[07:38:07]] [SUCCESS] Screenshot refreshed successfully
[[07:38:07]] [SUCCESS] Screenshot refreshed successfully
[[07:38:06]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:38:06]] [SUCCESS] Screenshot refreshed
[[07:38:06]] [INFO] Refreshing screenshot...
[[07:38:01]] [SUCCESS] Screenshot refreshed successfully
[[07:38:01]] [SUCCESS] Screenshot refreshed successfully
[[07:38:01]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[07:38:01]] [SUCCESS] Screenshot refreshed
[[07:38:01]] [INFO] Refreshing screenshot...
[[07:37:57]] [SUCCESS] Screenshot refreshed successfully
[[07:37:57]] [SUCCESS] Screenshot refreshed successfully
[[07:37:57]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:37:56]] [SUCCESS] Screenshot refreshed
[[07:37:56]] [INFO] Refreshing screenshot...
[[07:37:52]] [SUCCESS] Screenshot refreshed successfully
[[07:37:52]] [SUCCESS] Screenshot refreshed successfully
[[07:37:52]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[07:37:51]] [SUCCESS] Screenshot refreshed
[[07:37:51]] [INFO] Refreshing screenshot...
[[07:37:48]] [SUCCESS] Screenshot refreshed successfully
[[07:37:48]] [SUCCESS] Screenshot refreshed successfully
[[07:37:48]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:37:47]] [SUCCESS] Screenshot refreshed
[[07:37:47]] [INFO] Refreshing screenshot...
[[07:37:42]] [SUCCESS] Screenshot refreshed successfully
[[07:37:42]] [SUCCESS] Screenshot refreshed successfully
[[07:37:42]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:37:42]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[07:37:42]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[07:37:42]] [INFO] Executing action 242/512: Execute Test Case: Kmart-Signin (8 steps)
[[07:37:41]] [SUCCESS] Screenshot refreshed
[[07:37:41]] [INFO] Refreshing screenshot...
[[07:37:39]] [SUCCESS] Screenshot refreshed successfully
[[07:37:39]] [SUCCESS] Screenshot refreshed successfully
[[07:37:38]] [INFO] Executing action 241/512: iOS Function: alert_accept
[[07:37:38]] [SUCCESS] Screenshot refreshed
[[07:37:38]] [INFO] Refreshing screenshot...
[[07:37:32]] [SUCCESS] Screenshot refreshed successfully
[[07:37:32]] [SUCCESS] Screenshot refreshed successfully
[[07:37:31]] [INFO] Executing action 240/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:37:31]] [SUCCESS] Screenshot refreshed
[[07:37:31]] [INFO] Refreshing screenshot...
[[07:37:24]] [SUCCESS] Screenshot refreshed successfully
[[07:37:24]] [SUCCESS] Screenshot refreshed successfully
[[07:37:24]] [INFO] Executing action 239/512: Wait for 5 ms
[[07:37:23]] [SUCCESS] Screenshot refreshed
[[07:37:23]] [INFO] Refreshing screenshot...
[[07:37:10]] [SUCCESS] Screenshot refreshed successfully
[[07:37:10]] [SUCCESS] Screenshot refreshed successfully
[[07:37:09]] [INFO] Executing action 238/512: Restart app: env[appid]
[[07:37:09]] [SUCCESS] Screenshot refreshed
[[07:37:09]] [INFO] Refreshing screenshot...
[[07:37:09]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[07:37:09]] [INFO] Executing action 237/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[07:37:08]] [SUCCESS] Screenshot refreshed
[[07:37:08]] [INFO] Refreshing screenshot...
[[07:37:05]] [SUCCESS] Screenshot refreshed successfully
[[07:37:05]] [SUCCESS] Screenshot refreshed successfully
[[07:37:05]] [INFO] Executing action 236/512: Terminate app: env[appid]
[[07:37:04]] [SUCCESS] Screenshot refreshed
[[07:37:04]] [INFO] Refreshing screenshot...
[[07:37:00]] [SUCCESS] Screenshot refreshed successfully
[[07:37:00]] [SUCCESS] Screenshot refreshed successfully
[[07:37:00]] [INFO] Executing action 235/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:37:00]] [SUCCESS] Screenshot refreshed
[[07:37:00]] [INFO] Refreshing screenshot...
[[07:36:53]] [SUCCESS] Screenshot refreshed successfully
[[07:36:53]] [SUCCESS] Screenshot refreshed successfully
[[07:36:53]] [INFO] Executing action 234/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:36:53]] [SUCCESS] Screenshot refreshed
[[07:36:53]] [INFO] Refreshing screenshot...
[[07:36:50]] [SUCCESS] Screenshot refreshed successfully
[[07:36:50]] [SUCCESS] Screenshot refreshed successfully
[[07:36:49]] [INFO] Executing action 233/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:36:49]] [SUCCESS] Screenshot refreshed
[[07:36:49]] [INFO] Refreshing screenshot...
[[07:36:44]] [INFO] Executing action 232/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:36:44]] [SUCCESS] Screenshot refreshed successfully
[[07:36:44]] [SUCCESS] Screenshot refreshed successfully
[[07:36:44]] [SUCCESS] Screenshot refreshed
[[07:36:44]] [INFO] Refreshing screenshot...
[[07:36:40]] [SUCCESS] Screenshot refreshed successfully
[[07:36:40]] [SUCCESS] Screenshot refreshed successfully
[[07:36:40]] [INFO] Executing action 231/512: Tap on element with xpath: //XCUIElementTypeLink[@name="<NAME_EMAIL>"]
[[07:36:39]] [SUCCESS] Screenshot refreshed
[[07:36:39]] [INFO] Refreshing screenshot...
[[07:36:36]] [SUCCESS] Screenshot refreshed successfully
[[07:36:36]] [SUCCESS] Screenshot refreshed successfully
[[07:36:36]] [INFO] Executing action 230/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign in with Google"]
[[07:36:35]] [SUCCESS] Screenshot refreshed
[[07:36:35]] [INFO] Refreshing screenshot...
[[07:36:29]] [SUCCESS] Screenshot refreshed successfully
[[07:36:29]] [SUCCESS] Screenshot refreshed successfully
[[07:36:29]] [INFO] Executing action 229/512: swipeTillVisible action
[[07:36:28]] [SUCCESS] Screenshot refreshed
[[07:36:28]] [INFO] Refreshing screenshot...
[[07:36:25]] [SUCCESS] Screenshot refreshed successfully
[[07:36:25]] [SUCCESS] Screenshot refreshed successfully
[[07:36:25]] [INFO] Executing action 228/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:36:24]] [SUCCESS] Screenshot refreshed
[[07:36:24]] [INFO] Refreshing screenshot...
[[07:36:22]] [SUCCESS] Screenshot refreshed successfully
[[07:36:22]] [SUCCESS] Screenshot refreshed successfully
[[07:36:22]] [INFO] Executing action 227/512: iOS Function: alert_accept
[[07:36:21]] [SUCCESS] Screenshot refreshed
[[07:36:21]] [INFO] Refreshing screenshot...
[[07:36:16]] [SUCCESS] Screenshot refreshed successfully
[[07:36:16]] [SUCCESS] Screenshot refreshed successfully
[[07:36:16]] [INFO] Executing action 226/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:36:15]] [SUCCESS] Screenshot refreshed
[[07:36:15]] [INFO] Refreshing screenshot...
[[07:36:12]] [SUCCESS] Screenshot refreshed successfully
[[07:36:12]] [SUCCESS] Screenshot refreshed successfully
[[07:36:12]] [INFO] Executing action 225/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:36:11]] [SUCCESS] Screenshot refreshed
[[07:36:11]] [INFO] Refreshing screenshot...
[[07:36:07]] [SUCCESS] Screenshot refreshed successfully
[[07:36:07]] [SUCCESS] Screenshot refreshed successfully
[[07:36:07]] [INFO] Executing action 224/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:36:07]] [SUCCESS] Screenshot refreshed
[[07:36:07]] [INFO] Refreshing screenshot...
[[07:36:00]] [SUCCESS] Screenshot refreshed successfully
[[07:36:00]] [SUCCESS] Screenshot refreshed successfully
[[07:36:00]] [INFO] Executing action 223/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:36:00]] [SUCCESS] Screenshot refreshed
[[07:36:00]] [INFO] Refreshing screenshot...
[[07:35:56]] [SUCCESS] Screenshot refreshed successfully
[[07:35:56]] [SUCCESS] Screenshot refreshed successfully
[[07:35:56]] [INFO] Executing action 222/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:35:55]] [SUCCESS] Screenshot refreshed
[[07:35:55]] [INFO] Refreshing screenshot...
[[07:35:49]] [INFO] Executing action 221/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:35:49]] [SUCCESS] Screenshot refreshed successfully
[[07:35:49]] [SUCCESS] Screenshot refreshed successfully
[[07:35:48]] [SUCCESS] Screenshot refreshed
[[07:35:48]] [INFO] Refreshing screenshot...
[[07:35:45]] [INFO] Executing action 220/512: Tap on element with xpath: //XCUIElementTypeButton[@name="4"]
[[07:35:45]] [SUCCESS] Screenshot refreshed successfully
[[07:35:45]] [SUCCESS] Screenshot refreshed successfully
[[07:35:45]] [SUCCESS] Screenshot refreshed
[[07:35:45]] [INFO] Refreshing screenshot...
[[07:35:42]] [INFO] Executing action 219/512: Tap on element with xpath: //XCUIElementTypeButton[@name="3"]
[[07:35:42]] [SUCCESS] Screenshot refreshed successfully
[[07:35:42]] [SUCCESS] Screenshot refreshed successfully
[[07:35:41]] [SUCCESS] Screenshot refreshed
[[07:35:41]] [INFO] Refreshing screenshot...
[[07:35:38]] [INFO] Executing action 218/512: Tap on element with xpath: //XCUIElementTypeButton[@name="2"]
[[07:35:38]] [SUCCESS] Screenshot refreshed successfully
[[07:35:38]] [SUCCESS] Screenshot refreshed successfully
[[07:35:38]] [SUCCESS] Screenshot refreshed
[[07:35:38]] [INFO] Refreshing screenshot...
[[07:35:35]] [INFO] Executing action 217/512: Tap on element with xpath: //XCUIElementTypeButton[@name="1"]
[[07:35:35]] [SUCCESS] Screenshot refreshed successfully
[[07:35:35]] [SUCCESS] Screenshot refreshed successfully
[[07:35:34]] [SUCCESS] Screenshot refreshed
[[07:35:34]] [INFO] Refreshing screenshot...
[[07:35:31]] [INFO] Executing action 216/512: Tap on element with xpath: //XCUIElementTypeButton[@name="9"]
[[07:35:31]] [SUCCESS] Screenshot refreshed successfully
[[07:35:31]] [SUCCESS] Screenshot refreshed successfully
[[07:35:31]] [SUCCESS] Screenshot refreshed
[[07:35:31]] [INFO] Refreshing screenshot...
[[07:35:28]] [INFO] Executing action 215/512: Tap on element with xpath: //XCUIElementTypeButton[@name="5"]
[[07:35:28]] [SUCCESS] Screenshot refreshed successfully
[[07:35:28]] [SUCCESS] Screenshot refreshed successfully
[[07:35:27]] [SUCCESS] Screenshot refreshed
[[07:35:27]] [INFO] Refreshing screenshot...
[[07:35:24]] [INFO] Executing action 214/512: Tap on Text: "Passcode"
[[07:35:24]] [SUCCESS] Screenshot refreshed successfully
[[07:35:24]] [SUCCESS] Screenshot refreshed successfully
[[07:35:24]] [SUCCESS] Screenshot refreshed
[[07:35:24]] [INFO] Refreshing screenshot...
[[07:35:12]] [SUCCESS] Screenshot refreshed successfully
[[07:35:12]] [SUCCESS] Screenshot refreshed successfully
[[07:35:12]] [INFO] Executing action 213/512: Wait for 10 ms
[[07:35:12]] [SUCCESS] Screenshot refreshed
[[07:35:12]] [INFO] Refreshing screenshot...
[[07:35:07]] [SUCCESS] Screenshot refreshed successfully
[[07:35:07]] [SUCCESS] Screenshot refreshed successfully
[[07:35:07]] [INFO] Executing action 212/512: Tap on Text: "Apple"
[[07:35:06]] [SUCCESS] Screenshot refreshed
[[07:35:06]] [INFO] Refreshing screenshot...
[[07:35:02]] [SUCCESS] Screenshot refreshed successfully
[[07:35:02]] [SUCCESS] Screenshot refreshed successfully
[[07:35:02]] [INFO] Executing action 211/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:35:01]] [SUCCESS] Screenshot refreshed
[[07:35:01]] [INFO] Refreshing screenshot...
[[07:34:58]] [SUCCESS] Screenshot refreshed successfully
[[07:34:58]] [SUCCESS] Screenshot refreshed successfully
[[07:34:58]] [INFO] Executing action 210/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:34:58]] [SUCCESS] Screenshot refreshed
[[07:34:58]] [INFO] Refreshing screenshot...
[[07:34:55]] [SUCCESS] Screenshot refreshed successfully
[[07:34:55]] [SUCCESS] Screenshot refreshed successfully
[[07:34:55]] [INFO] Executing action 209/512: iOS Function: alert_accept
[[07:34:54]] [SUCCESS] Screenshot refreshed
[[07:34:54]] [INFO] Refreshing screenshot...
[[07:34:49]] [SUCCESS] Screenshot refreshed successfully
[[07:34:49]] [SUCCESS] Screenshot refreshed successfully
[[07:34:49]] [INFO] Executing action 208/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:34:48]] [SUCCESS] Screenshot refreshed
[[07:34:48]] [INFO] Refreshing screenshot...
[[07:34:45]] [SUCCESS] Screenshot refreshed successfully
[[07:34:45]] [SUCCESS] Screenshot refreshed successfully
[[07:34:45]] [INFO] Executing action 207/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:34:44]] [SUCCESS] Screenshot refreshed
[[07:34:44]] [INFO] Refreshing screenshot...
[[07:34:40]] [INFO] Executing action 206/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:34:40]] [SUCCESS] Screenshot refreshed successfully
[[07:34:40]] [SUCCESS] Screenshot refreshed successfully
[[07:34:40]] [SUCCESS] Screenshot refreshed
[[07:34:40]] [INFO] Refreshing screenshot...
[[07:34:35]] [SUCCESS] Screenshot refreshed successfully
[[07:34:35]] [SUCCESS] Screenshot refreshed successfully
[[07:34:35]] [INFO] Executing action 205/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:34:34]] [SUCCESS] Screenshot refreshed
[[07:34:34]] [INFO] Refreshing screenshot...
[[07:34:31]] [SUCCESS] Screenshot refreshed successfully
[[07:34:31]] [SUCCESS] Screenshot refreshed successfully
[[07:34:31]] [INFO] Executing action 204/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:34:30]] [SUCCESS] Screenshot refreshed
[[07:34:30]] [INFO] Refreshing screenshot...
[[07:34:26]] [SUCCESS] Screenshot refreshed successfully
[[07:34:26]] [SUCCESS] Screenshot refreshed successfully
[[07:34:26]] [INFO] Executing action 203/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:34:25]] [SUCCESS] Screenshot refreshed
[[07:34:25]] [INFO] Refreshing screenshot...
[[07:34:21]] [SUCCESS] Screenshot refreshed successfully
[[07:34:21]] [SUCCESS] Screenshot refreshed successfully
[[07:34:21]] [INFO] Executing action 202/512: iOS Function: text
[[07:34:20]] [SUCCESS] Screenshot refreshed
[[07:34:20]] [INFO] Refreshing screenshot...
[[07:34:17]] [SUCCESS] Screenshot refreshed successfully
[[07:34:17]] [SUCCESS] Screenshot refreshed successfully
[[07:34:16]] [INFO] Executing action 201/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[07:34:16]] [SUCCESS] Screenshot refreshed
[[07:34:16]] [INFO] Refreshing screenshot...
[[07:34:11]] [SUCCESS] Screenshot refreshed successfully
[[07:34:11]] [SUCCESS] Screenshot refreshed successfully
[[07:34:11]] [INFO] Executing action 200/512: iOS Function: text
[[07:34:11]] [SUCCESS] Screenshot refreshed
[[07:34:11]] [INFO] Refreshing screenshot...
[[07:34:07]] [SUCCESS] Screenshot refreshed successfully
[[07:34:07]] [SUCCESS] Screenshot refreshed successfully
[[07:34:07]] [INFO] Executing action 199/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[07:34:06]] [SUCCESS] Screenshot refreshed
[[07:34:06]] [INFO] Refreshing screenshot...
[[07:34:02]] [SUCCESS] Screenshot refreshed successfully
[[07:34:02]] [SUCCESS] Screenshot refreshed successfully
[[07:34:02]] [INFO] Executing action 198/512: Tap on Text: "OnePass"
[[07:34:01]] [SUCCESS] Screenshot refreshed
[[07:34:01]] [INFO] Refreshing screenshot...
[[07:33:57]] [SUCCESS] Screenshot refreshed successfully
[[07:33:57]] [SUCCESS] Screenshot refreshed successfully
[[07:33:57]] [INFO] Executing action 197/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:33:56]] [SUCCESS] Screenshot refreshed
[[07:33:56]] [INFO] Refreshing screenshot...
[[07:33:53]] [SUCCESS] Screenshot refreshed successfully
[[07:33:53]] [SUCCESS] Screenshot refreshed successfully
[[07:33:53]] [INFO] Executing action 196/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:33:52]] [SUCCESS] Screenshot refreshed
[[07:33:52]] [INFO] Refreshing screenshot...
[[07:33:50]] [SUCCESS] Screenshot refreshed successfully
[[07:33:50]] [SUCCESS] Screenshot refreshed successfully
[[07:33:50]] [INFO] Executing action 195/512: iOS Function: alert_accept
[[07:33:49]] [SUCCESS] Screenshot refreshed
[[07:33:49]] [INFO] Refreshing screenshot...
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:44]] [SUCCESS] Screenshot refreshed successfully
[[07:33:44]] [INFO] Executing action 194/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:33:43]] [SUCCESS] Screenshot refreshed
[[07:33:43]] [INFO] Refreshing screenshot...
[[07:33:40]] [SUCCESS] Screenshot refreshed successfully
[[07:33:40]] [SUCCESS] Screenshot refreshed successfully
[[07:33:39]] [INFO] Executing action 193/512: Check if element with accessibility_id="txtHomeAccountCtaSignIn" exists
[[07:33:39]] [SUCCESS] Screenshot refreshed
[[07:33:39]] [INFO] Refreshing screenshot...
[[07:33:35]] [SUCCESS] Screenshot refreshed successfully
[[07:33:35]] [SUCCESS] Screenshot refreshed successfully
[[07:33:35]] [INFO] Executing action 192/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:33:35]] [SUCCESS] Screenshot refreshed
[[07:33:35]] [INFO] Refreshing screenshot...
[[07:33:28]] [SUCCESS] Screenshot refreshed successfully
[[07:33:28]] [SUCCESS] Screenshot refreshed successfully
[[07:33:28]] [INFO] Executing action 191/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:33:28]] [SUCCESS] Screenshot refreshed
[[07:33:28]] [INFO] Refreshing screenshot...
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:25]] [SUCCESS] Screenshot refreshed successfully
[[07:33:24]] [INFO] Executing action 190/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:33:24]] [SUCCESS] Screenshot refreshed
[[07:33:24]] [INFO] Refreshing screenshot...
[[07:33:21]] [SUCCESS] Screenshot refreshed successfully
[[07:33:21]] [SUCCESS] Screenshot refreshed successfully
[[07:33:20]] [INFO] Executing action 189/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:33:20]] [SUCCESS] Screenshot refreshed
[[07:33:20]] [INFO] Refreshing screenshot...
[[07:33:15]] [SUCCESS] Screenshot refreshed successfully
[[07:33:15]] [SUCCESS] Screenshot refreshed successfully
[[07:33:15]] [INFO] Executing action 188/512: iOS Function: text
[[07:33:14]] [SUCCESS] Screenshot refreshed
[[07:33:14]] [INFO] Refreshing screenshot...
[[07:33:11]] [SUCCESS] Screenshot refreshed successfully
[[07:33:11]] [SUCCESS] Screenshot refreshed successfully
[[07:33:11]] [INFO] Executing action 187/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:33:10]] [SUCCESS] Screenshot refreshed
[[07:33:10]] [INFO] Refreshing screenshot...
[[07:33:06]] [SUCCESS] Screenshot refreshed successfully
[[07:33:06]] [SUCCESS] Screenshot refreshed successfully
[[07:33:06]] [INFO] Executing action 186/512: iOS Function: text
[[07:33:05]] [SUCCESS] Screenshot refreshed
[[07:33:05]] [INFO] Refreshing screenshot...
[[07:33:02]] [SUCCESS] Screenshot refreshed successfully
[[07:33:02]] [SUCCESS] Screenshot refreshed successfully
[[07:33:01]] [INFO] Executing action 185/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:33:01]] [SUCCESS] Screenshot refreshed
[[07:33:01]] [INFO] Refreshing screenshot...
[[07:32:58]] [SUCCESS] Screenshot refreshed successfully
[[07:32:58]] [SUCCESS] Screenshot refreshed successfully
[[07:32:58]] [INFO] Executing action 184/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:32:57]] [SUCCESS] Screenshot refreshed
[[07:32:57]] [INFO] Refreshing screenshot...
[[07:32:55]] [SUCCESS] Screenshot refreshed successfully
[[07:32:55]] [SUCCESS] Screenshot refreshed successfully
[[07:32:54]] [INFO] Executing action 183/512: iOS Function: alert_accept
[[07:32:54]] [SUCCESS] Screenshot refreshed
[[07:32:54]] [INFO] Refreshing screenshot...
[[07:32:48]] [SUCCESS] Screenshot refreshed successfully
[[07:32:48]] [SUCCESS] Screenshot refreshed successfully
[[07:32:47]] [INFO] Executing action 182/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:32:47]] [SUCCESS] Screenshot refreshed
[[07:32:47]] [INFO] Refreshing screenshot...
[[07:32:32]] [SUCCESS] Screenshot refreshed successfully
[[07:32:32]] [SUCCESS] Screenshot refreshed successfully
[[07:32:32]] [INFO] Executing action 181/512: Restart app: env[appid]
[[07:32:31]] [SUCCESS] Screenshot refreshed
[[07:32:31]] [INFO] Refreshing screenshot...
[[07:32:27]] [SUCCESS] Screenshot refreshed successfully
[[07:32:27]] [SUCCESS] Screenshot refreshed successfully
[[07:32:27]] [INFO] Executing action 180/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:32:27]] [SUCCESS] Screenshot refreshed
[[07:32:27]] [INFO] Refreshing screenshot...
[[07:32:20]] [SUCCESS] Screenshot refreshed successfully
[[07:32:20]] [SUCCESS] Screenshot refreshed successfully
[[07:32:20]] [INFO] Executing action 179/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:32:20]] [SUCCESS] Screenshot refreshed
[[07:32:20]] [INFO] Refreshing screenshot...
[[07:32:16]] [SUCCESS] Screenshot refreshed successfully
[[07:32:16]] [SUCCESS] Screenshot refreshed successfully
[[07:32:16]] [INFO] Executing action 178/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:32:16]] [SUCCESS] Screenshot refreshed
[[07:32:16]] [INFO] Refreshing screenshot...
[[07:32:12]] [SUCCESS] Screenshot refreshed successfully
[[07:32:12]] [SUCCESS] Screenshot refreshed successfully
[[07:32:12]] [INFO] Executing action 177/512: Tap on Text: "Remove"
[[07:32:11]] [SUCCESS] Screenshot refreshed
[[07:32:11]] [INFO] Refreshing screenshot...
[[07:32:07]] [SUCCESS] Screenshot refreshed successfully
[[07:32:07]] [SUCCESS] Screenshot refreshed successfully
[[07:32:07]] [INFO] Executing action 176/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:32:07]] [SUCCESS] Screenshot refreshed
[[07:32:07]] [INFO] Refreshing screenshot...
[[07:32:02]] [SUCCESS] Screenshot refreshed successfully
[[07:32:02]] [SUCCESS] Screenshot refreshed successfully
[[07:32:02]] [INFO] Executing action 175/512: Tap on Text: "Remove"
[[07:32:02]] [SUCCESS] Screenshot refreshed
[[07:32:02]] [INFO] Refreshing screenshot...
[[07:31:58]] [SUCCESS] Screenshot refreshed successfully
[[07:31:58]] [SUCCESS] Screenshot refreshed successfully
[[07:31:58]] [INFO] Executing action 174/512: If exists: xpath="(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:31:57]] [SUCCESS] Screenshot refreshed
[[07:31:57]] [INFO] Refreshing screenshot...
[[07:31:53]] [SUCCESS] Screenshot refreshed successfully
[[07:31:53]] [SUCCESS] Screenshot refreshed successfully
[[07:31:53]] [INFO] Executing action 173/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:31:52]] [SUCCESS] Screenshot refreshed
[[07:31:52]] [INFO] Refreshing screenshot...
[[07:31:48]] [SUCCESS] Screenshot refreshed successfully
[[07:31:48]] [SUCCESS] Screenshot refreshed successfully
[[07:31:48]] [INFO] Executing action 172/512: Tap on image: banner-close-updated.png
[[07:31:48]] [SUCCESS] Screenshot refreshed
[[07:31:48]] [INFO] Refreshing screenshot...
[[07:31:44]] [SUCCESS] Screenshot refreshed successfully
[[07:31:44]] [SUCCESS] Screenshot refreshed successfully
[[07:31:44]] [INFO] Executing action 171/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Move to wishlist"]
[[07:31:44]] [SUCCESS] Screenshot refreshed
[[07:31:44]] [INFO] Refreshing screenshot...
[[07:31:40]] [SUCCESS] Screenshot refreshed successfully
[[07:31:40]] [SUCCESS] Screenshot refreshed successfully
[[07:31:40]] [INFO] Executing action 170/512: Wait till xpath=//XCUIElementTypeButton[@name="Move to wishlist"]
[[07:31:40]] [SUCCESS] Screenshot refreshed
[[07:31:40]] [INFO] Refreshing screenshot...
[[07:31:36]] [SUCCESS] Screenshot refreshed successfully
[[07:31:36]] [SUCCESS] Screenshot refreshed successfully
[[07:31:36]] [INFO] Executing action 169/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:31:35]] [SUCCESS] Screenshot refreshed
[[07:31:35]] [INFO] Refreshing screenshot...
[[07:31:32]] [SUCCESS] Screenshot refreshed successfully
[[07:31:32]] [SUCCESS] Screenshot refreshed successfully
[[07:31:31]] [INFO] Executing action 168/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:31:31]] [SUCCESS] Screenshot refreshed
[[07:31:31]] [INFO] Refreshing screenshot...
[[07:31:27]] [SUCCESS] Screenshot refreshed successfully
[[07:31:27]] [SUCCESS] Screenshot refreshed successfully
[[07:31:27]] [INFO] Executing action 167/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:31:26]] [SUCCESS] Screenshot refreshed
[[07:31:26]] [INFO] Refreshing screenshot...
[[07:31:22]] [SUCCESS] Screenshot refreshed successfully
[[07:31:22]] [SUCCESS] Screenshot refreshed successfully
[[07:31:22]] [INFO] Executing action 166/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[1]
[[07:31:22]] [SUCCESS] Screenshot refreshed
[[07:31:22]] [INFO] Refreshing screenshot...
[[07:31:18]] [SUCCESS] Screenshot refreshed successfully
[[07:31:18]] [SUCCESS] Screenshot refreshed successfully
[[07:31:18]] [INFO] Executing action 165/512: Tap on Text: "Remove"
[[07:31:17]] [SUCCESS] Screenshot refreshed
[[07:31:17]] [INFO] Refreshing screenshot...
[[07:31:14]] [SUCCESS] Screenshot refreshed successfully
[[07:31:14]] [SUCCESS] Screenshot refreshed successfully
[[07:31:14]] [INFO] Executing action 164/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[2]/following-sibling::XCUIElementTypeImage[2]
[[07:31:13]] [SUCCESS] Screenshot refreshed
[[07:31:13]] [INFO] Refreshing screenshot...
[[07:31:09]] [SUCCESS] Screenshot refreshed successfully
[[07:31:09]] [SUCCESS] Screenshot refreshed successfully
[[07:31:09]] [INFO] Executing action 163/512: Tap on Text: "Move"
[[07:31:08]] [SUCCESS] Screenshot refreshed
[[07:31:08]] [INFO] Refreshing screenshot...
[[07:31:05]] [SUCCESS] Screenshot refreshed successfully
[[07:31:05]] [SUCCESS] Screenshot refreshed successfully
[[07:31:05]] [INFO] Executing action 162/512: Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:31:04]] [SUCCESS] Screenshot refreshed
[[07:31:04]] [INFO] Refreshing screenshot...
[[07:31:02]] [SUCCESS] Screenshot refreshed successfully
[[07:31:02]] [SUCCESS] Screenshot refreshed successfully
[[07:31:01]] [INFO] Executing action 161/512: Wait till xpath=(//XCUIElementTypeOther[contains(@name,"txtPrice")])[1]/following-sibling::XCUIElementTypeImage[2]
[[07:31:01]] [SUCCESS] Screenshot refreshed
[[07:31:01]] [INFO] Refreshing screenshot...
[[07:30:57]] [SUCCESS] Screenshot refreshed successfully
[[07:30:57]] [SUCCESS] Screenshot refreshed successfully
[[07:30:57]] [INFO] Executing action 160/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:30:56]] [SUCCESS] Screenshot refreshed
[[07:30:56]] [INFO] Refreshing screenshot...
[[07:30:52]] [SUCCESS] Screenshot refreshed successfully
[[07:30:52]] [SUCCESS] Screenshot refreshed successfully
[[07:30:52]] [INFO] Executing action 159/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:30:52]] [SUCCESS] Screenshot refreshed
[[07:30:52]] [INFO] Refreshing screenshot...
[[07:30:46]] [SUCCESS] Screenshot refreshed successfully
[[07:30:46]] [SUCCESS] Screenshot refreshed successfully
[[07:30:45]] [INFO] Executing action 158/512: swipeTillVisible action
[[07:30:45]] [SUCCESS] Screenshot refreshed
[[07:30:45]] [INFO] Refreshing screenshot...
[[07:30:41]] [SUCCESS] Screenshot refreshed successfully
[[07:30:41]] [SUCCESS] Screenshot refreshed successfully
[[07:30:41]] [INFO] Executing action 157/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:30:40]] [SUCCESS] Screenshot refreshed
[[07:30:40]] [INFO] Refreshing screenshot...
[[07:30:37]] [SUCCESS] Screenshot refreshed successfully
[[07:30:37]] [SUCCESS] Screenshot refreshed successfully
[[07:30:37]] [INFO] Executing action 156/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:30:36]] [SUCCESS] Screenshot refreshed
[[07:30:36]] [INFO] Refreshing screenshot...
[[07:30:33]] [SUCCESS] Screenshot refreshed successfully
[[07:30:33]] [SUCCESS] Screenshot refreshed successfully
[[07:30:32]] [INFO] Executing action 155/512: iOS Function: text
[[07:30:32]] [SUCCESS] Screenshot refreshed
[[07:30:32]] [INFO] Refreshing screenshot...
[[07:30:27]] [SUCCESS] Screenshot refreshed successfully
[[07:30:27]] [SUCCESS] Screenshot refreshed successfully
[[07:30:26]] [INFO] Executing action 154/512: Tap on Text: "Find"
[[07:30:26]] [SUCCESS] Screenshot refreshed
[[07:30:26]] [INFO] Refreshing screenshot...
[[07:30:21]] [SUCCESS] Screenshot refreshed successfully
[[07:30:21]] [SUCCESS] Screenshot refreshed successfully
[[07:30:21]] [INFO] Executing action 153/512: Restart app: env[appid]
[[07:30:20]] [SUCCESS] Screenshot refreshed
[[07:30:20]] [INFO] Refreshing screenshot...
[[07:30:16]] [SUCCESS] Screenshot refreshed successfully
[[07:30:16]] [SUCCESS] Screenshot refreshed successfully
[[07:30:16]] [INFO] Executing action 152/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:30:16]] [SUCCESS] Screenshot refreshed
[[07:30:16]] [INFO] Refreshing screenshot...
[[07:30:09]] [SUCCESS] Screenshot refreshed successfully
[[07:30:09]] [SUCCESS] Screenshot refreshed successfully
[[07:30:09]] [INFO] Executing action 151/512: swipeTillVisible action
[[07:30:08]] [SUCCESS] Screenshot refreshed
[[07:30:08]] [INFO] Refreshing screenshot...
[[07:30:05]] [SUCCESS] Screenshot refreshed successfully
[[07:30:05]] [SUCCESS] Screenshot refreshed successfully
[[07:30:05]] [INFO] Executing action 150/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:30:04]] [SUCCESS] Screenshot refreshed
[[07:30:04]] [INFO] Refreshing screenshot...
[[07:29:49]] [SUCCESS] Screenshot refreshed successfully
[[07:29:49]] [SUCCESS] Screenshot refreshed successfully
[[07:29:49]] [INFO] Executing action 149/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="You may also like"]/following-sibling::*[1]//XCUIElementTypeLink)[1]
[[07:29:48]] [SUCCESS] Screenshot refreshed
[[07:29:48]] [INFO] Refreshing screenshot...
[[07:29:44]] [SUCCESS] Screenshot refreshed successfully
[[07:29:44]] [SUCCESS] Screenshot refreshed successfully
[[07:29:44]] [INFO] Executing action 148/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[07:29:44]] [SUCCESS] Screenshot refreshed
[[07:29:44]] [INFO] Refreshing screenshot...
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [SUCCESS] Screenshot refreshed successfully
[[07:29:37]] [INFO] Executing action 147/512: swipeTillVisible action
[[07:29:36]] [SUCCESS] Screenshot refreshed
[[07:29:36]] [INFO] Refreshing screenshot...
[[07:29:33]] [SUCCESS] Screenshot refreshed successfully
[[07:29:33]] [SUCCESS] Screenshot refreshed successfully
[[07:29:32]] [INFO] Executing action 146/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:29:32]] [SUCCESS] Screenshot refreshed
[[07:29:32]] [INFO] Refreshing screenshot...
[[07:29:28]] [SUCCESS] Screenshot refreshed successfully
[[07:29:28]] [SUCCESS] Screenshot refreshed successfully
[[07:29:28]] [INFO] Executing action 145/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:29:27]] [SUCCESS] Screenshot refreshed
[[07:29:27]] [INFO] Refreshing screenshot...
[[07:29:24]] [SUCCESS] Screenshot refreshed successfully
[[07:29:24]] [SUCCESS] Screenshot refreshed successfully
[[07:29:24]] [INFO] Executing action 144/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:29:23]] [SUCCESS] Screenshot refreshed
[[07:29:23]] [INFO] Refreshing screenshot...
[[07:29:19]] [SUCCESS] Screenshot refreshed successfully
[[07:29:19]] [SUCCESS] Screenshot refreshed successfully
[[07:29:19]] [INFO] Executing action 143/512: iOS Function: text
[[07:29:18]] [SUCCESS] Screenshot refreshed
[[07:29:18]] [INFO] Refreshing screenshot...
[[07:29:13]] [SUCCESS] Screenshot refreshed successfully
[[07:29:13]] [SUCCESS] Screenshot refreshed successfully
[[07:29:13]] [INFO] Executing action 142/512: Tap on Text: "Find"
[[07:29:12]] [SUCCESS] Screenshot refreshed
[[07:29:12]] [INFO] Refreshing screenshot...
[[07:29:07]] [SUCCESS] Screenshot refreshed successfully
[[07:29:07]] [SUCCESS] Screenshot refreshed successfully
[[07:29:07]] [INFO] Executing action 141/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:29:06]] [SUCCESS] Screenshot refreshed
[[07:29:06]] [INFO] Refreshing screenshot...
[[07:29:02]] [SUCCESS] Screenshot refreshed successfully
[[07:29:02]] [SUCCESS] Screenshot refreshed successfully
[[07:29:02]] [INFO] Executing action 140/512: iOS Function: text
[[07:29:01]] [SUCCESS] Screenshot refreshed
[[07:29:01]] [INFO] Refreshing screenshot...
[[07:28:57]] [SUCCESS] Screenshot refreshed successfully
[[07:28:57]] [SUCCESS] Screenshot refreshed successfully
[[07:28:57]] [INFO] Executing action 139/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:28:57]] [SUCCESS] Screenshot refreshed
[[07:28:57]] [INFO] Refreshing screenshot...
[[07:28:52]] [SUCCESS] Screenshot refreshed successfully
[[07:28:52]] [SUCCESS] Screenshot refreshed successfully
[[07:28:52]] [INFO] Executing action 138/512: iOS Function: text
[[07:28:52]] [SUCCESS] Screenshot refreshed
[[07:28:52]] [INFO] Refreshing screenshot...
[[07:28:48]] [SUCCESS] Screenshot refreshed successfully
[[07:28:48]] [SUCCESS] Screenshot refreshed successfully
[[07:28:48]] [INFO] Executing action 137/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:28:48]] [SUCCESS] Screenshot refreshed
[[07:28:48]] [INFO] Refreshing screenshot...
[[07:28:44]] [SUCCESS] Screenshot refreshed successfully
[[07:28:44]] [SUCCESS] Screenshot refreshed successfully
[[07:28:44]] [INFO] Executing action 136/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:28:44]] [SUCCESS] Screenshot refreshed
[[07:28:44]] [INFO] Refreshing screenshot...
[[07:28:42]] [SUCCESS] Screenshot refreshed successfully
[[07:28:42]] [SUCCESS] Screenshot refreshed successfully
[[07:28:41]] [INFO] Executing action 135/512: iOS Function: alert_accept
[[07:28:41]] [SUCCESS] Screenshot refreshed
[[07:28:41]] [INFO] Refreshing screenshot...
[[07:28:35]] [SUCCESS] Screenshot refreshed successfully
[[07:28:35]] [SUCCESS] Screenshot refreshed successfully
[[07:28:34]] [INFO] Executing action 134/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:28:34]] [SUCCESS] Screenshot refreshed
[[07:28:34]] [INFO] Refreshing screenshot...
[[07:28:19]] [SUCCESS] Screenshot refreshed successfully
[[07:28:19]] [SUCCESS] Screenshot refreshed successfully
[[07:28:18]] [INFO] Executing action 133/512: Restart app: env[appid]
[[07:28:18]] [SUCCESS] Screenshot refreshed
[[07:28:18]] [INFO] Refreshing screenshot...
[[07:28:18]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[07:28:18]] [INFO] Executing action 132/512: Hook Action: tap on image: banner-close-updated.png (Recovery)
[[07:28:17]] [SUCCESS] Screenshot refreshed
[[07:28:17]] [INFO] Refreshing screenshot...
[[07:28:13]] [SUCCESS] Screenshot refreshed successfully
[[07:28:13]] [SUCCESS] Screenshot refreshed successfully
[[07:28:13]] [INFO] Executing action 131/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:28:13]] [SUCCESS] Screenshot refreshed
[[07:28:13]] [INFO] Refreshing screenshot...
[[07:28:06]] [SUCCESS] Screenshot refreshed successfully
[[07:28:06]] [SUCCESS] Screenshot refreshed successfully
[[07:28:06]] [INFO] Executing action 130/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:28:06]] [SUCCESS] Screenshot refreshed
[[07:28:06]] [INFO] Refreshing screenshot...
[[07:28:03]] [SUCCESS] Screenshot refreshed successfully
[[07:28:03]] [SUCCESS] Screenshot refreshed successfully
[[07:28:02]] [INFO] Executing action 129/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:28:02]] [SUCCESS] Screenshot refreshed
[[07:28:02]] [INFO] Refreshing screenshot...
[[07:28:01]] [SUCCESS] Screenshot refreshed successfully
[[07:28:01]] [SUCCESS] Screenshot refreshed successfully
[[07:28:00]] [INFO] Executing action 128/512: Add Log: Sign in from universal login page successful (with screenshot)
[[07:28:00]] [SUCCESS] Screenshot refreshed
[[07:28:00]] [INFO] Refreshing screenshot...
[[07:27:59]] [SUCCESS] Screenshot refreshed
[[07:27:59]] [INFO] Refreshing screenshot...
[[07:27:57]] [SUCCESS] Screenshot refreshed successfully
[[07:27:57]] [SUCCESS] Screenshot refreshed successfully
[[07:27:56]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:27:56]] [SUCCESS] Screenshot refreshed
[[07:27:56]] [INFO] Refreshing screenshot...
[[07:27:51]] [SUCCESS] Screenshot refreshed successfully
[[07:27:51]] [SUCCESS] Screenshot refreshed successfully
[[07:27:51]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[07:27:50]] [SUCCESS] Screenshot refreshed
[[07:27:50]] [INFO] Refreshing screenshot...
[[07:27:47]] [SUCCESS] Screenshot refreshed successfully
[[07:27:47]] [SUCCESS] Screenshot refreshed successfully
[[07:27:47]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:27:46]] [SUCCESS] Screenshot refreshed
[[07:27:46]] [INFO] Refreshing screenshot...
[[07:27:42]] [SUCCESS] Screenshot refreshed successfully
[[07:27:42]] [SUCCESS] Screenshot refreshed successfully
[[07:27:42]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[07:27:41]] [SUCCESS] Screenshot refreshed
[[07:27:41]] [INFO] Refreshing screenshot...
[[07:27:38]] [SUCCESS] Screenshot refreshed successfully
[[07:27:38]] [SUCCESS] Screenshot refreshed successfully
[[07:27:37]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:27:37]] [SUCCESS] Screenshot refreshed
[[07:27:37]] [INFO] Refreshing screenshot...
[[07:27:32]] [SUCCESS] Screenshot refreshed successfully
[[07:27:32]] [SUCCESS] Screenshot refreshed successfully
[[07:27:32]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:27:32]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[07:27:32]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[07:27:32]] [INFO] Executing action 127/512: Execute Test Case: Kmart-Signin (6 steps)
[[07:27:31]] [SUCCESS] Screenshot refreshed
[[07:27:31]] [INFO] Refreshing screenshot...
[[07:27:29]] [SUCCESS] Screenshot refreshed successfully
[[07:27:29]] [SUCCESS] Screenshot refreshed successfully
[[07:27:28]] [INFO] Executing action 126/512: iOS Function: alert_accept
[[07:27:28]] [SUCCESS] Screenshot refreshed
[[07:27:28]] [INFO] Refreshing screenshot...
[[07:27:23]] [SUCCESS] Screenshot refreshed successfully
[[07:27:23]] [SUCCESS] Screenshot refreshed successfully
[[07:27:22]] [INFO] Executing action 125/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:27:22]] [SUCCESS] Screenshot refreshed
[[07:27:22]] [INFO] Refreshing screenshot...
[[07:27:18]] [SUCCESS] Screenshot refreshed successfully
[[07:27:18]] [SUCCESS] Screenshot refreshed successfully
[[07:27:18]] [INFO] Executing action 124/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:27:17]] [SUCCESS] Screenshot refreshed
[[07:27:17]] [INFO] Refreshing screenshot...
[[07:27:13]] [SUCCESS] Screenshot refreshed successfully
[[07:27:13]] [SUCCESS] Screenshot refreshed successfully
[[07:27:13]] [INFO] Executing action 123/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:27:13]] [SUCCESS] Screenshot refreshed
[[07:27:13]] [INFO] Refreshing screenshot...
[[07:27:07]] [SUCCESS] Screenshot refreshed successfully
[[07:27:07]] [SUCCESS] Screenshot refreshed successfully
[[07:27:06]] [INFO] Executing action 122/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:27:06]] [SUCCESS] Screenshot refreshed
[[07:27:06]] [INFO] Refreshing screenshot...
[[07:27:02]] [SUCCESS] Screenshot refreshed successfully
[[07:27:02]] [SUCCESS] Screenshot refreshed successfully
[[07:27:02]] [INFO] Executing action 121/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:27:01]] [SUCCESS] Screenshot refreshed
[[07:27:01]] [INFO] Refreshing screenshot...
[[07:27:00]] [SUCCESS] Screenshot refreshed successfully
[[07:27:00]] [SUCCESS] Screenshot refreshed successfully
[[07:27:00]] [INFO] Executing action 120/512: Add Log: Sign in from bag page is successfully (with screenshot)
[[07:26:59]] [SUCCESS] Screenshot refreshed
[[07:26:59]] [INFO] Refreshing screenshot...
[[07:26:55]] [SUCCESS] Screenshot refreshed successfully
[[07:26:55]] [SUCCESS] Screenshot refreshed successfully
[[07:26:55]] [INFO] Executing action 119/512: iOS Function: text
[[07:26:54]] [SUCCESS] Screenshot refreshed
[[07:26:54]] [INFO] Refreshing screenshot...
[[07:26:51]] [SUCCESS] Screenshot refreshed successfully
[[07:26:51]] [SUCCESS] Screenshot refreshed successfully
[[07:26:50]] [INFO] Executing action 118/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password*"]
[[07:26:50]] [SUCCESS] Screenshot refreshed
[[07:26:50]] [INFO] Refreshing screenshot...
[[07:26:46]] [SUCCESS] Screenshot refreshed successfully
[[07:26:46]] [SUCCESS] Screenshot refreshed successfully
[[07:26:45]] [INFO] Executing action 117/512: iOS Function: text
[[07:26:45]] [SUCCESS] Screenshot refreshed
[[07:26:45]] [INFO] Refreshing screenshot...
[[07:26:41]] [SUCCESS] Screenshot refreshed successfully
[[07:26:41]] [SUCCESS] Screenshot refreshed successfully
[[07:26:41]] [INFO] Executing action 116/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email*"]
[[07:26:41]] [SUCCESS] Screenshot refreshed
[[07:26:41]] [INFO] Refreshing screenshot...
[[07:26:38]] [SUCCESS] Screenshot refreshed successfully
[[07:26:38]] [SUCCESS] Screenshot refreshed successfully
[[07:26:38]] [INFO] Executing action 115/512: iOS Function: alert_accept
[[07:26:37]] [SUCCESS] Screenshot refreshed
[[07:26:37]] [INFO] Refreshing screenshot...
[[07:26:33]] [SUCCESS] Screenshot refreshed successfully
[[07:26:33]] [SUCCESS] Screenshot refreshed successfully
[[07:26:33]] [INFO] Executing action 114/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Sign In"]
[[07:26:33]] [SUCCESS] Screenshot refreshed
[[07:26:33]] [INFO] Refreshing screenshot...
[[07:26:22]] [SUCCESS] Screenshot refreshed successfully
[[07:26:22]] [SUCCESS] Screenshot refreshed successfully
[[07:26:22]] [INFO] Executing action 113/512: swipeTillVisible action
[[07:26:22]] [SUCCESS] Screenshot refreshed
[[07:26:22]] [INFO] Refreshing screenshot...
[[07:26:17]] [SUCCESS] Screenshot refreshed successfully
[[07:26:17]] [SUCCESS] Screenshot refreshed successfully
[[07:26:17]] [INFO] Executing action 112/512: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[07:26:17]] [SUCCESS] Screenshot refreshed
[[07:26:17]] [INFO] Refreshing screenshot...
[[07:26:14]] [SUCCESS] Screenshot refreshed successfully
[[07:26:14]] [SUCCESS] Screenshot refreshed successfully
[[07:26:13]] [INFO] Executing action 111/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:26:13]] [SUCCESS] Screenshot refreshed
[[07:26:13]] [INFO] Refreshing screenshot...
[[07:26:10]] [SUCCESS] Screenshot refreshed successfully
[[07:26:10]] [SUCCESS] Screenshot refreshed successfully
[[07:26:09]] [INFO] Executing action 110/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:26:09]] [SUCCESS] Screenshot refreshed
[[07:26:09]] [INFO] Refreshing screenshot...
[[07:26:05]] [SUCCESS] Screenshot refreshed successfully
[[07:26:05]] [SUCCESS] Screenshot refreshed successfully
[[07:26:04]] [INFO] Executing action 109/512: iOS Function: text
[[07:26:04]] [SUCCESS] Screenshot refreshed
[[07:26:04]] [INFO] Refreshing screenshot...
[[07:25:58]] [SUCCESS] Screenshot refreshed successfully
[[07:25:58]] [SUCCESS] Screenshot refreshed successfully
[[07:25:58]] [INFO] Executing action 108/512: Tap on Text: "Find"
[[07:25:58]] [SUCCESS] Screenshot refreshed
[[07:25:58]] [INFO] Refreshing screenshot...
[[07:25:55]] [SUCCESS] Screenshot refreshed successfully
[[07:25:55]] [SUCCESS] Screenshot refreshed successfully
[[07:25:54]] [INFO] Executing action 107/512: Restart app: env[appid]
[[07:25:54]] [SUCCESS] Screenshot refreshed
[[07:25:54]] [INFO] Refreshing screenshot...
[[07:25:50]] [INFO] Executing action 106/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:25:50]] [SUCCESS] Screenshot refreshed successfully
[[07:25:50]] [SUCCESS] Screenshot refreshed successfully
[[07:25:49]] [SUCCESS] Screenshot refreshed
[[07:25:49]] [INFO] Refreshing screenshot...
[[07:25:43]] [SUCCESS] Screenshot refreshed successfully
[[07:25:43]] [SUCCESS] Screenshot refreshed successfully
[[07:25:43]] [INFO] Executing action 105/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:25:42]] [SUCCESS] Screenshot refreshed
[[07:25:42]] [INFO] Refreshing screenshot...
[[07:25:39]] [SUCCESS] Screenshot refreshed successfully
[[07:25:39]] [SUCCESS] Screenshot refreshed successfully
[[07:25:39]] [INFO] Executing action 104/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:25:38]] [SUCCESS] Screenshot refreshed
[[07:25:38]] [INFO] Refreshing screenshot...
[[07:25:37]] [SUCCESS] Screenshot refreshed successfully
[[07:25:37]] [SUCCESS] Screenshot refreshed successfully
[[07:25:36]] [INFO] Executing action 103/512: Add Log: Sign in from wishlist page successfully (with screenshot)
[[07:25:36]] [SUCCESS] Screenshot refreshed
[[07:25:36]] [INFO] Refreshing screenshot...
[[07:25:33]] [SUCCESS] Screenshot refreshed successfully
[[07:25:33]] [SUCCESS] Screenshot refreshed successfully
[[07:25:32]] [INFO] Executing action 102/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:25:32]] [SUCCESS] Screenshot refreshed
[[07:25:32]] [INFO] Refreshing screenshot...
[[07:25:28]] [SUCCESS] Screenshot refreshed successfully
[[07:25:28]] [SUCCESS] Screenshot refreshed successfully
[[07:25:28]] [INFO] Executing action 101/512: Tap on element with xpath:  //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:25:27]] [SUCCESS] Screenshot refreshed
[[07:25:27]] [INFO] Refreshing screenshot...
[[07:25:23]] [SUCCESS] Screenshot refreshed successfully
[[07:25:23]] [SUCCESS] Screenshot refreshed successfully
[[07:25:23]] [INFO] Executing action 100/512: iOS Function: text
[[07:25:22]] [SUCCESS] Screenshot refreshed
[[07:25:22]] [INFO] Refreshing screenshot...
[[07:25:19]] [SUCCESS] Screenshot refreshed successfully
[[07:25:19]] [SUCCESS] Screenshot refreshed successfully
[[07:25:19]] [INFO] Executing action 99/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:25:18]] [SUCCESS] Screenshot refreshed
[[07:25:18]] [INFO] Refreshing screenshot...
[[07:25:14]] [SUCCESS] Screenshot refreshed successfully
[[07:25:14]] [SUCCESS] Screenshot refreshed successfully
[[07:25:14]] [INFO] Executing action 98/512: iOS Function: text
[[07:25:13]] [SUCCESS] Screenshot refreshed
[[07:25:13]] [INFO] Refreshing screenshot...
[[07:25:09]] [SUCCESS] Screenshot refreshed successfully
[[07:25:09]] [SUCCESS] Screenshot refreshed successfully
[[07:25:09]] [INFO] Executing action 97/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:25:09]] [SUCCESS] Screenshot refreshed
[[07:25:09]] [INFO] Refreshing screenshot...
[[07:25:06]] [SUCCESS] Screenshot refreshed successfully
[[07:25:06]] [SUCCESS] Screenshot refreshed successfully
[[07:25:05]] [INFO] Executing action 96/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:25:05]] [SUCCESS] Screenshot refreshed
[[07:25:05]] [INFO] Refreshing screenshot...
[[07:25:02]] [SUCCESS] Screenshot refreshed successfully
[[07:25:02]] [SUCCESS] Screenshot refreshed successfully
[[07:25:02]] [INFO] Executing action 95/512: iOS Function: alert_accept
[[07:25:02]] [SUCCESS] Screenshot refreshed
[[07:25:02]] [INFO] Refreshing screenshot...
[[07:24:58]] [SUCCESS] Screenshot refreshed successfully
[[07:24:58]] [SUCCESS] Screenshot refreshed successfully
[[07:24:58]] [INFO] Executing action 94/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtLog in"]
[[07:24:58]] [SUCCESS] Screenshot refreshed
[[07:24:58]] [INFO] Refreshing screenshot...
[[07:24:55]] [SUCCESS] Screenshot refreshed successfully
[[07:24:55]] [SUCCESS] Screenshot refreshed successfully
[[07:24:54]] [INFO] Executing action 93/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 3 of 5")]
[[07:24:54]] [SUCCESS] Screenshot refreshed
[[07:24:54]] [INFO] Refreshing screenshot...
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:50]] [SUCCESS] Screenshot refreshed successfully
[[07:24:50]] [INFO] Executing action 92/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:24:49]] [SUCCESS] Screenshot refreshed
[[07:24:49]] [INFO] Refreshing screenshot...
[[07:24:43]] [SUCCESS] Screenshot refreshed successfully
[[07:24:43]] [SUCCESS] Screenshot refreshed successfully
[[07:24:43]] [INFO] Executing action 91/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:24:42]] [SUCCESS] Screenshot refreshed
[[07:24:42]] [INFO] Refreshing screenshot...
[[07:24:39]] [SUCCESS] Screenshot refreshed successfully
[[07:24:39]] [SUCCESS] Screenshot refreshed successfully
[[07:24:39]] [INFO] Executing action 90/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:24:38]] [SUCCESS] Screenshot refreshed
[[07:24:38]] [INFO] Refreshing screenshot...
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:37]] [SUCCESS] Screenshot refreshed successfully
[[07:24:36]] [INFO] Executing action 89/512: Add Log: Sign in from home page sign in button successfully (with screenshot)
[[07:24:36]] [SUCCESS] Screenshot refreshed
[[07:24:36]] [INFO] Refreshing screenshot...
[[07:24:33]] [SUCCESS] Screenshot refreshed successfully
[[07:24:33]] [SUCCESS] Screenshot refreshed successfully
[[07:24:32]] [INFO] Executing action 88/512: Wait till xpath=//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]
[[07:24:32]] [SUCCESS] Screenshot refreshed
[[07:24:32]] [INFO] Refreshing screenshot...
[[07:24:27]] [SUCCESS] Screenshot refreshed successfully
[[07:24:27]] [SUCCESS] Screenshot refreshed successfully
[[07:24:27]] [INFO] Executing action 87/512: iOS Function: text
[[07:24:27]] [SUCCESS] Screenshot refreshed
[[07:24:27]] [INFO] Refreshing screenshot...
[[07:24:23]] [SUCCESS] Screenshot refreshed successfully
[[07:24:23]] [SUCCESS] Screenshot refreshed successfully
[[07:24:23]] [INFO] Executing action 86/512: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:24:22]] [SUCCESS] Screenshot refreshed
[[07:24:22]] [INFO] Refreshing screenshot...
[[07:24:18]] [SUCCESS] Screenshot refreshed successfully
[[07:24:18]] [SUCCESS] Screenshot refreshed successfully
[[07:24:18]] [INFO] Executing action 85/512: iOS Function: text
[[07:24:17]] [SUCCESS] Screenshot refreshed
[[07:24:17]] [INFO] Refreshing screenshot...
[[07:24:14]] [SUCCESS] Screenshot refreshed successfully
[[07:24:14]] [SUCCESS] Screenshot refreshed successfully
[[07:24:14]] [INFO] Executing action 84/512: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:24:13]] [SUCCESS] Screenshot refreshed
[[07:24:13]] [INFO] Refreshing screenshot...
[[07:24:10]] [SUCCESS] Screenshot refreshed successfully
[[07:24:10]] [SUCCESS] Screenshot refreshed successfully
[[07:24:10]] [INFO] Executing action 83/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:24:09]] [SUCCESS] Screenshot refreshed
[[07:24:09]] [INFO] Refreshing screenshot...
[[07:24:07]] [SUCCESS] Screenshot refreshed successfully
[[07:24:07]] [SUCCESS] Screenshot refreshed successfully
[[07:24:06]] [INFO] Executing action 82/512: iOS Function: alert_accept
[[07:24:06]] [SUCCESS] Screenshot refreshed
[[07:24:06]] [INFO] Refreshing screenshot...
[[07:24:00]] [SUCCESS] Screenshot refreshed successfully
[[07:24:00]] [SUCCESS] Screenshot refreshed successfully
[[07:24:00]] [INFO] Executing action 81/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:23:59]] [SUCCESS] Screenshot refreshed
[[07:23:59]] [INFO] Refreshing screenshot...
[[07:23:45]] [SUCCESS] Screenshot refreshed successfully
[[07:23:45]] [SUCCESS] Screenshot refreshed successfully
[[07:23:44]] [INFO] Executing action 80/512: Restart app: env[appid]
[[07:23:44]] [SUCCESS] Screenshot refreshed
[[07:23:44]] [INFO] Refreshing screenshot...
[[07:23:43]] [SUCCESS] Screenshot refreshed
[[07:23:43]] [INFO] Refreshing screenshot...
[[07:23:40]] [SUCCESS] Screenshot refreshed successfully
[[07:23:40]] [SUCCESS] Screenshot refreshed successfully
[[07:23:39]] [INFO] Executing Multi Step action step 34/34: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Continue shopping"]
[[07:23:39]] [SUCCESS] Screenshot refreshed
[[07:23:39]] [INFO] Refreshing screenshot...
[[07:23:35]] [INFO] Executing Multi Step action step 33/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:23:35]] [SUCCESS] Screenshot refreshed successfully
[[07:23:35]] [SUCCESS] Screenshot refreshed successfully
[[07:23:35]] [SUCCESS] Screenshot refreshed
[[07:23:35]] [INFO] Refreshing screenshot...
[[07:23:31]] [SUCCESS] Screenshot refreshed successfully
[[07:23:31]] [SUCCESS] Screenshot refreshed successfully
[[07:23:31]] [INFO] Executing Multi Step action step 32/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:23:30]] [SUCCESS] Screenshot refreshed
[[07:23:30]] [INFO] Refreshing screenshot...
[[07:23:26]] [SUCCESS] Screenshot refreshed successfully
[[07:23:26]] [SUCCESS] Screenshot refreshed successfully
[[07:23:26]] [INFO] Executing Multi Step action step 31/34: Tap on image: banner-close-updated.png
[[07:23:25]] [SUCCESS] Screenshot refreshed
[[07:23:25]] [INFO] Refreshing screenshot...
[[07:23:16]] [SUCCESS] Screenshot refreshed successfully
[[07:23:16]] [SUCCESS] Screenshot refreshed successfully
[[07:23:16]] [INFO] Executing Multi Step action step 30/34: Swipe from (50%, 70%) to (50%, 30%)
[[07:23:15]] [SUCCESS] Screenshot refreshed
[[07:23:15]] [INFO] Refreshing screenshot...
[[07:23:11]] [SUCCESS] Screenshot refreshed successfully
[[07:23:11]] [SUCCESS] Screenshot refreshed successfully
[[07:23:11]] [INFO] Executing Multi Step action step 29/34: Tap on image: env[delivery-address-img]
[[07:23:11]] [SUCCESS] Screenshot refreshed
[[07:23:11]] [INFO] Refreshing screenshot...
[[07:23:07]] [SUCCESS] Screenshot refreshed successfully
[[07:23:07]] [SUCCESS] Screenshot refreshed successfully
[[07:23:07]] [INFO] Executing Multi Step action step 28/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Done"]
[[07:23:06]] [SUCCESS] Screenshot refreshed
[[07:23:06]] [INFO] Refreshing screenshot...
[[07:22:59]] [SUCCESS] Screenshot refreshed successfully
[[07:22:59]] [SUCCESS] Screenshot refreshed successfully
[[07:22:59]] [INFO] Executing Multi Step action step 27/34: Tap and Type at (54, 314): "305 238 Flinders"
[[07:22:59]] [SUCCESS] Screenshot refreshed
[[07:22:59]] [INFO] Refreshing screenshot...
[[07:22:54]] [SUCCESS] Screenshot refreshed successfully
[[07:22:54]] [SUCCESS] Screenshot refreshed successfully
[[07:22:54]] [INFO] Executing Multi Step action step 26/34: Tap on Text: "address"
[[07:22:53]] [SUCCESS] Screenshot refreshed
[[07:22:53]] [INFO] Refreshing screenshot...
[[07:22:49]] [SUCCESS] Screenshot refreshed successfully
[[07:22:49]] [SUCCESS] Screenshot refreshed successfully
[[07:22:49]] [INFO] Executing Multi Step action step 25/34: iOS Function: text
[[07:22:49]] [SUCCESS] Screenshot refreshed
[[07:22:49]] [INFO] Refreshing screenshot...
[[07:22:45]] [SUCCESS] Screenshot refreshed successfully
[[07:22:45]] [SUCCESS] Screenshot refreshed successfully
[[07:22:45]] [INFO] Executing Multi Step action step 24/34: textClear action
[[07:22:44]] [SUCCESS] Screenshot refreshed
[[07:22:44]] [INFO] Refreshing screenshot...
[[07:22:41]] [SUCCESS] Screenshot refreshed successfully
[[07:22:41]] [SUCCESS] Screenshot refreshed successfully
[[07:22:41]] [INFO] Executing Multi Step action step 23/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Mobile number"]
[[07:22:40]] [SUCCESS] Screenshot refreshed
[[07:22:40]] [INFO] Refreshing screenshot...
[[07:22:36]] [SUCCESS] Screenshot refreshed successfully
[[07:22:36]] [SUCCESS] Screenshot refreshed successfully
[[07:22:36]] [INFO] Executing Multi Step action step 22/34: textClear action
[[07:22:35]] [SUCCESS] Screenshot refreshed
[[07:22:35]] [INFO] Refreshing screenshot...
[[07:22:32]] [SUCCESS] Screenshot refreshed successfully
[[07:22:32]] [SUCCESS] Screenshot refreshed successfully
[[07:22:32]] [INFO] Executing Multi Step action step 21/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:22:31]] [SUCCESS] Screenshot refreshed
[[07:22:31]] [INFO] Refreshing screenshot...
[[07:22:27]] [SUCCESS] Screenshot refreshed successfully
[[07:22:27]] [SUCCESS] Screenshot refreshed successfully
[[07:22:27]] [INFO] Executing Multi Step action step 20/34: textClear action
[[07:22:27]] [SUCCESS] Screenshot refreshed
[[07:22:27]] [INFO] Refreshing screenshot...
[[07:22:23]] [SUCCESS] Screenshot refreshed successfully
[[07:22:23]] [SUCCESS] Screenshot refreshed successfully
[[07:22:23]] [INFO] Executing Multi Step action step 19/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="Last Name"]
[[07:22:23]] [SUCCESS] Screenshot refreshed
[[07:22:23]] [INFO] Refreshing screenshot...
[[07:22:19]] [SUCCESS] Screenshot refreshed successfully
[[07:22:19]] [SUCCESS] Screenshot refreshed successfully
[[07:22:18]] [INFO] Executing Multi Step action step 18/34: textClear action
[[07:22:18]] [SUCCESS] Screenshot refreshed
[[07:22:18]] [INFO] Refreshing screenshot...
[[07:22:15]] [SUCCESS] Screenshot refreshed successfully
[[07:22:15]] [SUCCESS] Screenshot refreshed successfully
[[07:22:15]] [INFO] Executing Multi Step action step 17/34: Tap on element with xpath: //XCUIElementTypeTextField[@name="First Name"]
[[07:22:14]] [SUCCESS] Screenshot refreshed
[[07:22:14]] [INFO] Refreshing screenshot...
[[07:22:10]] [SUCCESS] Screenshot refreshed successfully
[[07:22:10]] [SUCCESS] Screenshot refreshed successfully
[[07:22:10]] [INFO] Executing Multi Step action step 16/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Continue to details"]
[[07:22:10]] [SUCCESS] Screenshot refreshed
[[07:22:10]] [INFO] Refreshing screenshot...
[[07:21:51]] [SUCCESS] Screenshot refreshed successfully
[[07:21:51]] [SUCCESS] Screenshot refreshed successfully
[[07:21:51]] [INFO] Executing Multi Step action step 15/34: swipeTillVisible action
[[07:21:51]] [SUCCESS] Screenshot refreshed
[[07:21:51]] [INFO] Refreshing screenshot...
[[07:21:47]] [SUCCESS] Screenshot refreshed successfully
[[07:21:47]] [SUCCESS] Screenshot refreshed successfully
[[07:21:47]] [INFO] Executing Multi Step action step 14/34: Tap on element with xpath: //XCUIElementTypeButton[@name="Delivery"]
[[07:21:47]] [SUCCESS] Screenshot refreshed
[[07:21:47]] [INFO] Refreshing screenshot...
[[07:21:43]] [INFO] Executing Multi Step action step 13/34: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[[07:21:43]] [SUCCESS] Screenshot refreshed successfully
[[07:21:43]] [SUCCESS] Screenshot refreshed successfully
[[07:21:43]] [SUCCESS] Screenshot refreshed
[[07:21:43]] [INFO] Refreshing screenshot...
[[07:21:39]] [SUCCESS] Screenshot refreshed successfully
[[07:21:39]] [SUCCESS] Screenshot refreshed successfully
[[07:21:39]] [INFO] Executing Multi Step action step 12/34: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:21:38]] [SUCCESS] Screenshot refreshed
[[07:21:38]] [INFO] Refreshing screenshot...
[[07:21:33]] [SUCCESS] Screenshot refreshed successfully
[[07:21:33]] [SUCCESS] Screenshot refreshed successfully
[[07:21:32]] [INFO] Executing Multi Step action step 11/34: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[07:21:32]] [SUCCESS] Screenshot refreshed
[[07:21:32]] [INFO] Refreshing screenshot...
[[07:21:29]] [SUCCESS] Screenshot refreshed successfully
[[07:21:29]] [SUCCESS] Screenshot refreshed successfully
[[07:21:28]] [INFO] Executing Multi Step action step 10/34: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:21:28]] [SUCCESS] Screenshot refreshed
[[07:21:28]] [INFO] Refreshing screenshot...
[[07:21:24]] [SUCCESS] Screenshot refreshed successfully
[[07:21:24]] [SUCCESS] Screenshot refreshed successfully
[[07:21:24]] [INFO] Executing Multi Step action step 9/34: iOS Function: text
[[07:21:23]] [SUCCESS] Screenshot refreshed
[[07:21:23]] [INFO] Refreshing screenshot...
[[07:21:18]] [SUCCESS] Screenshot refreshed successfully
[[07:21:18]] [SUCCESS] Screenshot refreshed successfully
[[07:21:18]] [INFO] Executing Multi Step action step 8/34: Tap on Text: "Find"
[[07:21:17]] [SUCCESS] Screenshot refreshed
[[07:21:17]] [INFO] Refreshing screenshot...
[[07:20:55]] [SUCCESS] Screenshot refreshed successfully
[[07:20:55]] [SUCCESS] Screenshot refreshed successfully
[[07:20:55]] [INFO] Executing Multi Step action step 7/34: If exists: xpath="//XCUIElementTypeOther[@name="txtLocationTitle"]/preceding-sibling::XCUIElementTypeButton[1]" (timeout: 20s) → Then tap at (0, 0)
[[07:20:54]] [SUCCESS] Screenshot refreshed
[[07:20:54]] [INFO] Refreshing screenshot...
[[07:20:42]] [SUCCESS] Screenshot refreshed successfully
[[07:20:42]] [SUCCESS] Screenshot refreshed successfully
[[07:20:42]] [INFO] Executing Multi Step action step 6/34: If exists: accessibility_id="btnUpdate" (timeout: 10s) → Then click element: accessibility_id="btnUpdate"
[[07:20:41]] [SUCCESS] Screenshot refreshed
[[07:20:41]] [INFO] Refreshing screenshot...
[[07:20:14]] [SUCCESS] Screenshot refreshed successfully
[[07:20:14]] [SUCCESS] Screenshot refreshed successfully
[[07:20:14]] [INFO] Executing Multi Step action step 5/34: If exists: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]" (timeout: 25s) → Then click element: xpath="//XCUIElementTypeButton[@name="Allow While Using App"]"
[[07:20:13]] [SUCCESS] Screenshot refreshed
[[07:20:13]] [INFO] Refreshing screenshot...
[[07:20:08]] [SUCCESS] Screenshot refreshed successfully
[[07:20:08]] [SUCCESS] Screenshot refreshed successfully
[[07:20:08]] [INFO] Executing Multi Step action step 4/34: Tap on Text: "Save"
[[07:20:08]] [SUCCESS] Screenshot refreshed
[[07:20:08]] [INFO] Refreshing screenshot...
[[07:20:03]] [SUCCESS] Screenshot refreshed successfully
[[07:20:03]] [SUCCESS] Screenshot refreshed successfully
[[07:20:03]] [INFO] Executing Multi Step action step 3/34: Tap on element with accessibility_id: btnCurrentLocationButton
[[07:20:02]] [SUCCESS] Screenshot refreshed
[[07:20:02]] [INFO] Refreshing screenshot...
[[07:19:58]] [SUCCESS] Screenshot refreshed successfully
[[07:19:58]] [SUCCESS] Screenshot refreshed successfully
[[07:19:58]] [INFO] Executing Multi Step action step 2/34: Wait till accessibility_id=btnCurrentLocationButton
[[07:19:58]] [SUCCESS] Screenshot refreshed
[[07:19:58]] [INFO] Refreshing screenshot...
[[07:19:51]] [SUCCESS] Screenshot refreshed successfully
[[07:19:51]] [SUCCESS] Screenshot refreshed successfully
[[07:19:51]] [INFO] Executing Multi Step action step 1/34: Tap on Text: "Edit"
[[07:19:51]] [INFO] Loaded 34 steps from test case: Delivery  Buy
[[07:19:50]] [INFO] Loading steps for Multi Step action: Delivery  Buy
[[07:19:50]] [INFO] Executing action 79/512: Execute Test Case: Delivery  Buy (34 steps)
[[07:19:50]] [SUCCESS] Screenshot refreshed
[[07:19:50]] [INFO] Refreshing screenshot...
[[07:19:46]] [SUCCESS] Screenshot refreshed successfully
[[07:19:46]] [SUCCESS] Screenshot refreshed successfully
[[07:19:46]] [INFO] Executing action 78/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[[07:19:45]] [SUCCESS] Screenshot refreshed
[[07:19:45]] [INFO] Refreshing screenshot...
[[07:19:42]] [SUCCESS] Screenshot refreshed successfully
[[07:19:42]] [SUCCESS] Screenshot refreshed successfully
[[07:19:42]] [INFO] Executing action 77/512: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[[07:19:41]] [SUCCESS] Screenshot refreshed
[[07:19:41]] [INFO] Refreshing screenshot...
[[07:19:35]] [SUCCESS] Screenshot refreshed successfully
[[07:19:35]] [SUCCESS] Screenshot refreshed successfully
[[07:19:35]] [INFO] Executing action 76/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:19:34]] [SUCCESS] Screenshot refreshed
[[07:19:34]] [INFO] Refreshing screenshot...
[[07:19:31]] [SUCCESS] Screenshot refreshed successfully
[[07:19:31]] [SUCCESS] Screenshot refreshed successfully
[[07:19:30]] [INFO] Executing action 75/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[[07:19:30]] [SUCCESS] Screenshot refreshed
[[07:19:30]] [INFO] Refreshing screenshot...
[[07:19:26]] [SUCCESS] Screenshot refreshed successfully
[[07:19:26]] [SUCCESS] Screenshot refreshed successfully
[[07:19:26]] [INFO] Executing action 74/512: Tap on image: banner-close-updated.png
[[07:19:25]] [SUCCESS] Screenshot refreshed
[[07:19:25]] [INFO] Refreshing screenshot...
[[07:19:22]] [SUCCESS] Screenshot refreshed successfully
[[07:19:22]] [SUCCESS] Screenshot refreshed successfully
[[07:19:22]] [INFO] Executing action 73/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:19:21]] [SUCCESS] Screenshot refreshed
[[07:19:21]] [INFO] Refreshing screenshot...
[[07:19:17]] [SUCCESS] Screenshot refreshed successfully
[[07:19:17]] [SUCCESS] Screenshot refreshed successfully
[[07:19:17]] [INFO] Executing action 72/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:19:16]] [SUCCESS] Screenshot refreshed
[[07:19:16]] [INFO] Refreshing screenshot...
[[07:19:13]] [INFO] Executing action 71/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[07:19:13]] [SUCCESS] Screenshot refreshed successfully
[[07:19:13]] [SUCCESS] Screenshot refreshed successfully
[[07:19:12]] [SUCCESS] Screenshot refreshed
[[07:19:12]] [INFO] Refreshing screenshot...
[[07:19:09]] [SUCCESS] Screenshot refreshed successfully
[[07:19:09]] [SUCCESS] Screenshot refreshed successfully
[[07:19:09]] [INFO] Executing action 70/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:19:08]] [SUCCESS] Screenshot refreshed
[[07:19:08]] [INFO] Refreshing screenshot...
[[07:19:04]] [SUCCESS] Screenshot refreshed successfully
[[07:19:04]] [SUCCESS] Screenshot refreshed successfully
[[07:19:04]] [INFO] Executing action 69/512: Tap on image: banner-close-updated.png
[[07:19:03]] [SUCCESS] Screenshot refreshed
[[07:19:03]] [INFO] Refreshing screenshot...
[[07:18:52]] [SUCCESS] Screenshot refreshed successfully
[[07:18:52]] [SUCCESS] Screenshot refreshed successfully
[[07:18:52]] [INFO] Executing action 68/512: Wait for 10 ms
[[07:18:51]] [SUCCESS] Screenshot refreshed
[[07:18:51]] [INFO] Refreshing screenshot...
[[07:18:47]] [SUCCESS] Screenshot refreshed successfully
[[07:18:47]] [SUCCESS] Screenshot refreshed successfully
[[07:18:46]] [INFO] Executing action 67/512: Tap on Text: "Brunswick"
[[07:18:46]] [SUCCESS] Screenshot refreshed
[[07:18:46]] [INFO] Refreshing screenshot...
[[07:18:42]] [SUCCESS] Screenshot refreshed successfully
[[07:18:42]] [SUCCESS] Screenshot refreshed successfully
[[07:18:42]] [INFO] Executing action 66/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:18:41]] [SUCCESS] Screenshot refreshed
[[07:18:41]] [INFO] Refreshing screenshot...
[[07:18:38]] [SUCCESS] Screenshot refreshed successfully
[[07:18:38]] [SUCCESS] Screenshot refreshed successfully
[[07:18:38]] [INFO] Executing action 65/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Click & Collect"]
[[07:18:37]] [SUCCESS] Screenshot refreshed
[[07:18:37]] [INFO] Refreshing screenshot...
[[07:18:34]] [INFO] Executing action 64/512: Check if element with xpath="//XCUIElementTypeButton[@name="Click & Collect"]" exists
[[07:18:34]] [SUCCESS] Screenshot refreshed successfully
[[07:18:34]] [SUCCESS] Screenshot refreshed successfully
[[07:18:34]] [SUCCESS] Screenshot refreshed
[[07:18:34]] [INFO] Refreshing screenshot...
[[07:18:31]] [SUCCESS] Screenshot refreshed successfully
[[07:18:31]] [SUCCESS] Screenshot refreshed successfully
[[07:18:30]] [INFO] Executing action 63/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:18:30]] [SUCCESS] Screenshot refreshed
[[07:18:30]] [INFO] Refreshing screenshot...
[[07:18:27]] [SUCCESS] Screenshot refreshed successfully
[[07:18:27]] [SUCCESS] Screenshot refreshed successfully
[[07:18:26]] [INFO] Executing action 62/512: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[[07:18:26]] [SUCCESS] Screenshot refreshed
[[07:18:26]] [INFO] Refreshing screenshot...
[[07:18:20]] [SUCCESS] Screenshot refreshed successfully
[[07:18:20]] [SUCCESS] Screenshot refreshed successfully
[[07:18:20]] [INFO] Executing action 61/512: Tap on element with accessibility_id: Add UNO Card Game - Red colour to bag Add
[[07:18:19]] [SUCCESS] Screenshot refreshed
[[07:18:19]] [INFO] Refreshing screenshot...
[[07:18:16]] [SUCCESS] Screenshot refreshed successfully
[[07:18:16]] [SUCCESS] Screenshot refreshed successfully
[[07:18:16]] [INFO] Executing action 60/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:18:15]] [SUCCESS] Screenshot refreshed
[[07:18:15]] [INFO] Refreshing screenshot...
[[07:18:11]] [SUCCESS] Screenshot refreshed successfully
[[07:18:11]] [SUCCESS] Screenshot refreshed successfully
[[07:18:11]] [INFO] Executing action 59/512: iOS Function: text
[[07:18:11]] [SUCCESS] Screenshot refreshed
[[07:18:11]] [INFO] Refreshing screenshot...
[[07:18:06]] [SUCCESS] Screenshot refreshed successfully
[[07:18:06]] [SUCCESS] Screenshot refreshed successfully
[[07:18:05]] [INFO] Executing action 58/512: Tap on Text: "Find"
[[07:18:05]] [SUCCESS] Screenshot refreshed
[[07:18:05]] [INFO] Refreshing screenshot...
[[07:18:04]] [SUCCESS] Screenshot refreshed
[[07:18:04]] [INFO] Refreshing screenshot...
[[07:18:02]] [SUCCESS] Screenshot refreshed successfully
[[07:18:02]] [SUCCESS] Screenshot refreshed successfully
[[07:18:01]] [INFO] Executing Multi Step action step 6/6: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[07:18:01]] [SUCCESS] Screenshot refreshed
[[07:18:01]] [INFO] Refreshing screenshot...
[[07:17:56]] [SUCCESS] Screenshot refreshed successfully
[[07:17:56]] [SUCCESS] Screenshot refreshed successfully
[[07:17:56]] [INFO] Executing Multi Step action step 5/6: iOS Function: text
[[07:17:55]] [SUCCESS] Screenshot refreshed
[[07:17:55]] [INFO] Refreshing screenshot...
[[07:17:52]] [SUCCESS] Screenshot refreshed successfully
[[07:17:52]] [SUCCESS] Screenshot refreshed successfully
[[07:17:52]] [INFO] Executing Multi Step action step 4/6: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[07:17:51]] [SUCCESS] Screenshot refreshed
[[07:17:51]] [INFO] Refreshing screenshot...
[[07:17:47]] [SUCCESS] Screenshot refreshed successfully
[[07:17:47]] [SUCCESS] Screenshot refreshed successfully
[[07:17:47]] [INFO] Executing Multi Step action step 3/6: iOS Function: text
[[07:17:46]] [SUCCESS] Screenshot refreshed
[[07:17:46]] [INFO] Refreshing screenshot...
[[07:17:43]] [SUCCESS] Screenshot refreshed successfully
[[07:17:43]] [SUCCESS] Screenshot refreshed successfully
[[07:17:42]] [INFO] Executing Multi Step action step 2/6: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[07:17:42]] [SUCCESS] Screenshot refreshed
[[07:17:42]] [INFO] Refreshing screenshot...
[[07:17:37]] [SUCCESS] Screenshot refreshed successfully
[[07:17:37]] [SUCCESS] Screenshot refreshed successfully
[[07:17:37]] [INFO] Executing Multi Step action step 1/6: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:17:37]] [INFO] Loaded 6 steps from test case: Kmart-Signin
[[07:17:37]] [INFO] Loading steps for Multi Step action: Kmart-Signin
[[07:17:37]] [INFO] Executing action 57/512: Execute Test Case: Kmart-Signin (8 steps)
[[07:17:36]] [SUCCESS] Screenshot refreshed
[[07:17:36]] [INFO] Refreshing screenshot...
[[07:17:33]] [SUCCESS] Screenshot refreshed successfully
[[07:17:33]] [SUCCESS] Screenshot refreshed successfully
[[07:17:33]] [INFO] Executing action 56/512: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[07:17:32]] [SUCCESS] Screenshot refreshed
[[07:17:32]] [INFO] Refreshing screenshot...
[[07:17:30]] [SUCCESS] Screenshot refreshed successfully
[[07:17:30]] [SUCCESS] Screenshot refreshed successfully
[[07:17:29]] [INFO] Executing action 55/512: iOS Function: alert_accept
[[07:17:29]] [SUCCESS] Screenshot refreshed
[[07:17:29]] [INFO] Refreshing screenshot...
[[07:17:23]] [SUCCESS] Screenshot refreshed successfully
[[07:17:23]] [SUCCESS] Screenshot refreshed successfully
[[07:17:22]] [INFO] Executing action 54/512: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[07:17:22]] [SUCCESS] Screenshot refreshed
[[07:17:22]] [INFO] Refreshing screenshot...
[[07:17:08]] [SUCCESS] Screenshot refreshed successfully
[[07:17:08]] [SUCCESS] Screenshot refreshed successfully
[[07:17:08]] [INFO] Executing action 53/512: Restart app: env[appid]
[[07:17:07]] [SUCCESS] Screenshot refreshed
[[07:17:07]] [INFO] Refreshing screenshot...
[[07:17:04]] [SUCCESS] Screenshot refreshed successfully
[[07:17:04]] [SUCCESS] Screenshot refreshed successfully
[[07:17:04]] [INFO] Executing action 52/512: Terminate app: env[appid]
[[07:17:04]] [SUCCESS] Screenshot refreshed
[[07:17:04]] [INFO] Refreshing screenshot...
[[07:17:00]] [SUCCESS] Screenshot refreshed successfully
[[07:17:00]] [SUCCESS] Screenshot refreshed successfully
[[07:17:00]] [INFO] Executing action 51/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:16:59]] [SUCCESS] Screenshot refreshed
[[07:16:59]] [INFO] Refreshing screenshot...
[[07:16:56]] [SUCCESS] Screenshot refreshed successfully
[[07:16:56]] [SUCCESS] Screenshot refreshed successfully
[[07:16:56]] [INFO] Executing action 50/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[[07:16:55]] [SUCCESS] Screenshot refreshed
[[07:16:55]] [INFO] Refreshing screenshot...
[[07:16:49]] [INFO] Executing action 49/512: swipeTillVisible action
[[07:16:49]] [SUCCESS] Screenshot refreshed successfully
[[07:16:49]] [SUCCESS] Screenshot refreshed successfully
[[07:16:49]] [SUCCESS] Screenshot refreshed
[[07:16:49]] [INFO] Refreshing screenshot...
[[07:16:45]] [SUCCESS] Screenshot refreshed successfully
[[07:16:45]] [SUCCESS] Screenshot refreshed successfully
[[07:16:44]] [INFO] Executing action 48/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[[07:16:44]] [SUCCESS] Screenshot refreshed
[[07:16:44]] [INFO] Refreshing screenshot...
[[07:16:37]] [SUCCESS] Screenshot refreshed successfully
[[07:16:37]] [SUCCESS] Screenshot refreshed successfully
[[07:16:37]] [INFO] Executing action 47/512: Tap on element with accessibility_id: Add to bag
[[07:16:37]] [SUCCESS] Screenshot refreshed
[[07:16:37]] [INFO] Refreshing screenshot...
[[07:16:33]] [SUCCESS] Screenshot refreshed successfully
[[07:16:33]] [SUCCESS] Screenshot refreshed successfully
[[07:16:33]] [INFO] Executing action 46/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[07:16:33]] [SUCCESS] Screenshot refreshed
[[07:16:33]] [INFO] Refreshing screenshot...
[[07:16:29]] [SUCCESS] Screenshot refreshed successfully
[[07:16:29]] [SUCCESS] Screenshot refreshed successfully
[[07:16:28]] [INFO] Executing action 45/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[07:16:28]] [SUCCESS] Screenshot refreshed
[[07:16:28]] [INFO] Refreshing screenshot...
[[07:16:25]] [SUCCESS] Screenshot refreshed successfully
[[07:16:25]] [SUCCESS] Screenshot refreshed successfully
[[07:16:24]] [INFO] Executing action 44/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:16:24]] [SUCCESS] Screenshot refreshed
[[07:16:24]] [INFO] Refreshing screenshot...
[[07:16:20]] [SUCCESS] Screenshot refreshed successfully
[[07:16:20]] [SUCCESS] Screenshot refreshed successfully
[[07:16:20]] [INFO] Executing action 43/512: iOS Function: text
[[07:16:19]] [SUCCESS] Screenshot refreshed
[[07:16:19]] [INFO] Refreshing screenshot...
[[07:16:16]] [SUCCESS] Screenshot refreshed successfully
[[07:16:16]] [SUCCESS] Screenshot refreshed successfully
[[07:16:16]] [INFO] Executing action 42/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[07:16:15]] [SUCCESS] Screenshot refreshed
[[07:16:15]] [INFO] Refreshing screenshot...
[[07:16:12]] [SUCCESS] Screenshot refreshed successfully
[[07:16:12]] [SUCCESS] Screenshot refreshed successfully
[[07:16:12]] [INFO] Executing action 41/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[07:16:11]] [SUCCESS] Screenshot refreshed
[[07:16:11]] [INFO] Refreshing screenshot...
[[07:16:07]] [SUCCESS] Screenshot refreshed successfully
[[07:16:07]] [SUCCESS] Screenshot refreshed successfully
[[07:16:07]] [INFO] Executing action 40/512: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[[07:16:06]] [SUCCESS] Screenshot refreshed
[[07:16:06]] [INFO] Refreshing screenshot...
[[07:16:03]] [SUCCESS] Screenshot refreshed successfully
[[07:16:03]] [SUCCESS] Screenshot refreshed successfully
[[07:16:03]] [INFO] Executing action 39/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:16:02]] [SUCCESS] Screenshot refreshed
[[07:16:02]] [INFO] Refreshing screenshot...
[[07:15:59]] [SUCCESS] Screenshot refreshed successfully
[[07:15:59]] [SUCCESS] Screenshot refreshed successfully
[[07:15:59]] [INFO] Executing action 38/512: iOS Function: text
[[07:15:58]] [SUCCESS] Screenshot refreshed
[[07:15:58]] [INFO] Refreshing screenshot...
[[07:15:55]] [SUCCESS] Screenshot refreshed successfully
[[07:15:55]] [SUCCESS] Screenshot refreshed successfully
[[07:15:55]] [INFO] Executing action 37/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[07:15:54]] [SUCCESS] Screenshot refreshed
[[07:15:54]] [INFO] Refreshing screenshot...
[[07:15:51]] [SUCCESS] Screenshot refreshed successfully
[[07:15:51]] [SUCCESS] Screenshot refreshed successfully
[[07:15:51]] [INFO] Executing action 36/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[07:15:50]] [SUCCESS] Screenshot refreshed
[[07:15:50]] [INFO] Refreshing screenshot...
[[07:15:45]] [SUCCESS] Screenshot refreshed successfully
[[07:15:45]] [SUCCESS] Screenshot refreshed successfully
[[07:15:45]] [INFO] Executing action 35/512: Restart app: env[appid]
[[07:15:44]] [SUCCESS] Screenshot refreshed
[[07:15:44]] [INFO] Refreshing screenshot...
[[07:15:41]] [SUCCESS] Screenshot refreshed successfully
[[07:15:41]] [SUCCESS] Screenshot refreshed successfully
[[07:15:41]] [INFO] Executing action 34/512: Tap on image: env[device-back-img]
[[07:15:40]] [SUCCESS] Screenshot refreshed
[[07:15:40]] [INFO] Refreshing screenshot...
[[07:15:37]] [SUCCESS] Screenshot refreshed successfully
[[07:15:37]] [SUCCESS] Screenshot refreshed successfully
[[07:15:37]] [INFO] Executing action 33/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtPostCodeSelectionScreenHeader"]" exists
[[07:15:37]] [SUCCESS] Screenshot refreshed
[[07:15:37]] [INFO] Refreshing screenshot...
[[07:15:33]] [SUCCESS] Screenshot refreshed successfully
[[07:15:33]] [SUCCESS] Screenshot refreshed successfully
[[07:15:33]] [INFO] Executing action 32/512: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Shop at"]/following-sibling::XCUIElementTypeButton
[[07:15:32]] [SUCCESS] Screenshot refreshed
[[07:15:32]] [INFO] Refreshing screenshot...
[[07:15:28]] [SUCCESS] Screenshot refreshed successfully
[[07:15:28]] [SUCCESS] Screenshot refreshed successfully
[[07:15:28]] [INFO] Executing action 31/512: Tap on image: env[paypal-close-img]
[[07:15:28]] [SUCCESS] Screenshot refreshed
[[07:15:28]] [INFO] Refreshing screenshot...
[[07:15:22]] [SUCCESS] Screenshot refreshed successfully
[[07:15:22]] [SUCCESS] Screenshot refreshed successfully
[[07:15:22]] [INFO] Executing action 30/512: Tap on element with accessibility_id: Learn more about PayPal Pay in 4
[[07:15:21]] [SUCCESS] Screenshot refreshed
[[07:15:21]] [INFO] Refreshing screenshot...
[[07:15:18]] [SUCCESS] Screenshot refreshed successfully
[[07:15:18]] [SUCCESS] Screenshot refreshed successfully
[[07:15:17]] [INFO] Executing action 29/512: Tap on image: env[device-back-img]
[[07:15:17]] [SUCCESS] Screenshot refreshed
[[07:15:17]] [INFO] Refreshing screenshot...
[[07:15:14]] [SUCCESS] Screenshot refreshed successfully
[[07:15:14]] [SUCCESS] Screenshot refreshed successfully
[[07:15:14]] [INFO] Executing action 28/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="What is Zip?"]" exists
[[07:15:14]] [SUCCESS] Screenshot refreshed
[[07:15:14]] [INFO] Refreshing screenshot...
[[07:15:08]] [SUCCESS] Screenshot refreshed successfully
[[07:15:08]] [SUCCESS] Screenshot refreshed successfully
[[07:15:08]] [INFO] Executing action 27/512: Tap on element with accessibility_id: Learn more about Zip
[[07:15:07]] [SUCCESS] Screenshot refreshed
[[07:15:07]] [INFO] Refreshing screenshot...
[[07:15:04]] [SUCCESS] Screenshot refreshed successfully
[[07:15:04]] [SUCCESS] Screenshot refreshed successfully
[[07:15:04]] [INFO] Executing action 26/512: Tap on image: env[device-back-img]
[[07:15:03]] [SUCCESS] Screenshot refreshed
[[07:15:03]] [INFO] Refreshing screenshot...
[[07:15:00]] [SUCCESS] Screenshot refreshed successfully
[[07:15:00]] [SUCCESS] Screenshot refreshed successfully
[[07:15:00]] [INFO] Executing action 25/512: Check if element with xpath="//XCUIElementTypeStaticText[@name="Afterpay – Now available in store"]" exists
[[07:15:00]] [SUCCESS] Screenshot refreshed
[[07:15:00]] [INFO] Refreshing screenshot...
[[07:14:54]] [SUCCESS] Screenshot refreshed successfully
[[07:14:54]] [SUCCESS] Screenshot refreshed successfully
[[07:14:54]] [INFO] Executing action 24/512: Tap on element with accessibility_id: Learn more about AfterPay
[[07:14:53]] [SUCCESS] Screenshot refreshed
[[07:14:53]] [INFO] Refreshing screenshot...
[[07:14:47]] [SUCCESS] Screenshot refreshed successfully
[[07:14:47]] [SUCCESS] Screenshot refreshed successfully
[[07:14:46]] [INFO] Executing action 23/512: swipeTillVisible action
[[07:14:46]] [SUCCESS] Screenshot refreshed
[[07:14:46]] [INFO] Refreshing screenshot...
[[07:14:42]] [SUCCESS] Screenshot refreshed successfully
[[07:14:42]] [SUCCESS] Screenshot refreshed successfully
[[07:14:41]] [INFO] Executing action 22/512: Tap on image: env[closebtnimage]
[[07:14:41]] [SUCCESS] Screenshot refreshed
[[07:14:41]] [INFO] Refreshing screenshot...
[[07:14:38]] [SUCCESS] Screenshot refreshed successfully
[[07:14:38]] [SUCCESS] Screenshot refreshed successfully
[[07:14:37]] [INFO] Executing action 21/512: Check if element with xpath="//XCUIElementTypeOther[contains(@name,"Check out ")]" exists
[[07:14:36]] [SUCCESS] Screenshot refreshed
[[07:14:36]] [INFO] Refreshing screenshot...
[[07:14:32]] [SUCCESS] Screenshot refreshed successfully
[[07:14:32]] [SUCCESS] Screenshot refreshed successfully
[[07:14:32]] [INFO] Executing action 20/512: Tap on image: env[product-share-img]
[[07:14:32]] [SUCCESS] Screenshot refreshed
[[07:14:32]] [INFO] Refreshing screenshot...
[[07:14:28]] [SUCCESS] Screenshot refreshed successfully
[[07:14:28]] [SUCCESS] Screenshot refreshed successfully
[[07:14:28]] [INFO] Executing action 19/512: Tap on element with xpath:  (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::* with fallback: Coordinates (98, 308)
[[07:14:27]] [SUCCESS] Screenshot refreshed
[[07:14:27]] [INFO] Refreshing screenshot...
[[07:14:24]] [SUCCESS] Screenshot refreshed successfully
[[07:14:24]] [SUCCESS] Screenshot refreshed successfully
[[07:14:24]] [INFO] Executing action 18/512: Wait till xpath= (//XCUIElementTypeButton[contains(@name,"bag Add")])[1]/parent::*
[[07:14:23]] [SUCCESS] Screenshot refreshed
[[07:14:23]] [INFO] Refreshing screenshot...
[[07:14:12]] [SUCCESS] Screenshot refreshed successfully
[[07:14:12]] [SUCCESS] Screenshot refreshed successfully
[[07:14:11]] [INFO] Executing action 17/512: Wait for 10 ms
[[07:14:11]] [SUCCESS] Screenshot refreshed
[[07:14:11]] [INFO] Refreshing screenshot...
[[07:14:06]] [SUCCESS] Screenshot refreshed successfully
[[07:14:06]] [SUCCESS] Screenshot refreshed successfully
[[07:14:06]] [INFO] Executing action 16/512: Swipe from (50%, 70%) to (50%, 30%)
[[07:14:05]] [SUCCESS] Screenshot refreshed
[[07:14:05]] [INFO] Refreshing screenshot...
[[07:14:02]] [SUCCESS] Screenshot refreshed successfully
[[07:14:02]] [SUCCESS] Screenshot refreshed successfully
[[07:14:01]] [INFO] Executing action 15/512: Tap on element with xpath: //XCUIElementTypeButton[@name="In stock only i... ChipsClose"]
[[07:14:01]] [SUCCESS] Screenshot refreshed
[[07:14:01]] [INFO] Refreshing screenshot...
[[07:13:58]] [SUCCESS] Screenshot refreshed successfully
[[07:13:58]] [SUCCESS] Screenshot refreshed successfully
[[07:13:58]] [INFO] Executing action 14/512: Check if element with xpath="//XCUIElementTypeButton[@name="In stock only i... ChipsClose"]" exists
[[07:13:57]] [SUCCESS] Screenshot refreshed
[[07:13:57]] [INFO] Refreshing screenshot...
[[07:13:54]] [SUCCESS] Screenshot refreshed successfully
[[07:13:54]] [SUCCESS] Screenshot refreshed successfully
[[07:13:54]] [INFO] Executing action 13/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Show")]
[[07:13:53]] [SUCCESS] Screenshot refreshed
[[07:13:53]] [INFO] Refreshing screenshot...
[[07:13:50]] [SUCCESS] Screenshot refreshed successfully
[[07:13:50]] [SUCCESS] Screenshot refreshed successfully
[[07:13:50]] [INFO] Executing action 12/512: Tap on element with xpath: //XCUIElementTypeSwitch[@name="Unchecked In stock only items"]
[[07:13:49]] [SUCCESS] Screenshot refreshed
[[07:13:49]] [INFO] Refreshing screenshot...
[[07:13:46]] [SUCCESS] Screenshot refreshed successfully
[[07:13:46]] [SUCCESS] Screenshot refreshed successfully
[[07:13:45]] [INFO] Executing action 11/512: Tap on element with xpath: //XCUIElementTypeButton[@name="Filter"]
[[07:13:45]] [SUCCESS] Screenshot refreshed
[[07:13:45]] [INFO] Refreshing screenshot...
[[07:13:42]] [SUCCESS] Screenshot refreshed successfully
[[07:13:42]] [SUCCESS] Screenshot refreshed successfully
[[07:13:41]] [INFO] Executing action 10/512: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[07:13:41]] [SUCCESS] Screenshot refreshed
[[07:13:41]] [INFO] Refreshing screenshot...
[[07:13:37]] [SUCCESS] Screenshot refreshed successfully
[[07:13:37]] [SUCCESS] Screenshot refreshed successfully
[[07:13:37]] [INFO] Executing action 9/512: Tap on Text: "Latest"
[[07:13:36]] [SUCCESS] Screenshot refreshed
[[07:13:36]] [INFO] Refreshing screenshot...
[[07:13:32]] [SUCCESS] Screenshot refreshed successfully
[[07:13:32]] [SUCCESS] Screenshot refreshed successfully
[[07:13:32]] [INFO] Executing action 8/512: Tap on Text: "Toys"
[[07:13:31]] [SUCCESS] Screenshot refreshed
[[07:13:31]] [INFO] Refreshing screenshot...
[[07:13:27]] [SUCCESS] Screenshot refreshed successfully
[[07:13:27]] [SUCCESS] Screenshot refreshed successfully
[[07:13:27]] [INFO] Executing action 7/512: Tap on image: env[device-back-img]
[[07:13:27]] [SUCCESS] Screenshot refreshed
[[07:13:27]] [INFO] Refreshing screenshot...
[[07:13:24]] [SUCCESS] Screenshot refreshed successfully
[[07:13:24]] [SUCCESS] Screenshot refreshed successfully
[[07:13:24]] [INFO] Executing action 6/512: Check if element with xpath="//XCUIElementTypeButton[@name="Scan barcode"]" exists
[[07:13:23]] [SUCCESS] Screenshot refreshed
[[07:13:23]] [INFO] Refreshing screenshot...
[[07:13:21]] [SUCCESS] Screenshot refreshed successfully
[[07:13:21]] [SUCCESS] Screenshot refreshed successfully
[[07:13:20]] [INFO] Executing action 5/512: Check if element with xpath="//XCUIElementTypeImage[@name="More"]" exists
[[07:13:20]] [SUCCESS] Screenshot refreshed
[[07:13:20]] [INFO] Refreshing screenshot...
[[07:13:16]] [SUCCESS] Screenshot refreshed successfully
[[07:13:16]] [SUCCESS] Screenshot refreshed successfully
[[07:13:16]] [INFO] Executing action 4/512: Tap on element with xpath: //XCUIElementTypeButton[@name="imgBtnSearch"]
[[07:13:16]] [SUCCESS] Screenshot refreshed
[[07:13:16]] [INFO] Refreshing screenshot...
[[07:13:13]] [SUCCESS] Screenshot refreshed successfully
[[07:13:13]] [SUCCESS] Screenshot refreshed successfully
[[07:13:13]] [INFO] Executing action 3/512: Wait till xpath=//XCUIElementTypeButton[@name="imgBtnSearch"]
[[07:13:12]] [SUCCESS] Screenshot refreshed
[[07:13:12]] [INFO] Refreshing screenshot...
[[07:13:09]] [SUCCESS] Screenshot refreshed successfully
[[07:13:09]] [SUCCESS] Screenshot refreshed successfully
[[07:13:09]] [INFO] Executing action 2/512: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 2 of 5")]
[[07:13:08]] [SUCCESS] Screenshot refreshed
[[07:13:08]] [INFO] Refreshing screenshot...
[[07:13:03]] [INFO] Executing action 1/512: Restart app: env[appid]
[[07:13:03]] [INFO] ExecutionManager: Starting execution of 512 actions...
[[07:13:03]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[07:13:03]] [INFO] Clearing screenshots from database before execution...
[[07:13:03]] [SUCCESS] All screenshots deleted successfully
[[07:13:03]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:13:03]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_071303/screenshots
[[07:13:03]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_071303
[[07:13:03]] [SUCCESS] Report directory initialized successfully
[[07:13:03]] [INFO] Initializing report directory and screenshots folder...
[[07:12:50]] [SUCCESS] All screenshots deleted successfully
[[07:12:50]] [INFO] All actions cleared
[[07:12:50]] [INFO] Cleaning up screenshots...
[[07:12:48]] [SUCCESS] Screenshot refreshed successfully
[[07:12:47]] [SUCCESS] Screenshot refreshed
[[07:12:47]] [INFO] Refreshing screenshot...
[[07:12:46]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[07:12:46]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[07:12:38]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[07:12:36]] [SUCCESS] Found 1 device(s)
[[07:12:35]] [INFO] Refreshing device list...

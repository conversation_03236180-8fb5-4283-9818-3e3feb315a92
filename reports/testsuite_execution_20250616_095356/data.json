{"name": "UI Execution 16/06/2025, 09:56:24", "testCases": [{"name": "Test Case", "status": "passed", "steps": [{"name": "Restart app: nz.com.kmart", "status": "passed", "duration": "3479ms", "action_id": "rkL0oz4kiL", "screenshot_filename": "rkL0oz4kiL.png", "report_screenshot": "rkL0oz4kiL.png", "resolved_screenshot": "screenshots/rkL0oz4kiL.png", "clean_action_id": "rkL0oz4kiL", "prefixed_action_id": "al_rkL0oz4kiL", "action_id_screenshot": "screenshots/rkL0oz4kiL.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "4044ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png"}, {"name": "iOS Function: alert_accept", "status": "passed", "duration": "1177ms", "action_id": "sc2KH9bG6H", "screenshot_filename": "sc2KH9bG6H.png", "report_screenshot": "sc2KH9bG6H.png", "resolved_screenshot": "screenshots/sc2KH9bG6H.png", "clean_action_id": "sc2KH9bG6H", "prefixed_action_id": "al_sc2KH9bG6H", "action_id_screenshot": "screenshots/sc2KH9bG6H.png"}, {"name": "Wait till xpath=//XCUIElementTypeTextField[@name=\"Email\"]", "status": "passed", "duration": "1586ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Execute Test Case: <PERSON><PERSON>-NZ-Signin (8 steps)", "status": "passed", "duration": "0ms", "action_id": "alaozIePOy", "screenshot_filename": "alaozIePOy.png", "report_screenshot": "alaozIePOy.png", "resolved_screenshot": "screenshots/alaozIePOy.png", "clean_action_id": "alaozIePOy", "prefixed_action_id": "al_alaozIePOy", "action_id_screenshot": "screenshots/alaozIePOy.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3855ms", "action_id": "0q79ZemF4g", "screenshot_filename": "0q79ZemF4g.png", "report_screenshot": "0q79ZemF4g.png", "resolved_screenshot": "screenshots/0q79ZemF4g.png", "clean_action_id": "0q79ZemF4g", "prefixed_action_id": "al_0q79ZemF4g", "action_id_screenshot": "screenshots/0q79ZemF4g.png"}, {"name": "iOS Function: text", "status": "passed", "duration": "2252ms", "action_id": "3caMBvQX7k", "screenshot_filename": "3caMBvQX7k.png", "report_screenshot": "3caMBvQX7k.png", "resolved_screenshot": "screenshots/3caMBvQX7k.png", "clean_action_id": "3caMBvQX7k", "prefixed_action_id": "al_3caMBvQX7k", "action_id_screenshot": "screenshots/3caMBvQX7k.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Filter\"]", "status": "passed", "duration": "1408ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1436ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "1855ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1691ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "passed", "duration": "2118ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: deviceback-se.png", "status": "passed", "duration": "2503ms", "action_id": "deviceback", "screenshot_filename": "deviceback.png", "report_screenshot": "deviceback.png", "resolved_screenshot": "screenshots/deviceback.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]", "status": "failed", "duration": "1878ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "passed", "duration": "1751ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "failed", "duration": "2146ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: deviceback-se.png", "status": "passed", "duration": "2320ms", "action_id": "deviceback", "screenshot_filename": "deviceback.png", "report_screenshot": "deviceback.png", "resolved_screenshot": "screenshots/deviceback.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]", "status": "passed", "duration": "11685ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "status": "unknown", "duration": "8847ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "2103ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1092ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1577ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Move\"", "status": "unknown", "duration": "2621ms", "action_id": "ZCdhKRqSd3", "screenshot_filename": "ZCdhKRqSd3.png", "report_screenshot": "ZCdhKRqSd3.png", "resolved_screenshot": "screenshots/ZCdhKRqSd3.png", "clean_action_id": "ZCdhKRqSd3", "prefixed_action_id": "al_ZCdhKRqSd3", "action_id_screenshot": "screenshots/ZCdhKRqSd3.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[2]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "1588ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2612ms", "action_id": "yUJyVO5Wev", "screenshot_filename": "yUJyVO5Wev.png", "report_screenshot": "yUJyVO5Wev.png", "resolved_screenshot": "screenshots/yUJyVO5Wev.png", "clean_action_id": "yUJyVO5Wev", "prefixed_action_id": "al_yUJyVO5Wev", "action_id_screenshot": "screenshots/yUJyVO5Wev.png"}, {"name": "Tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[1]", "status": "unknown", "duration": "1592ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeStaticText[@name=\"SKU :\"]", "status": "unknown", "duration": "1696ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "status": "unknown", "duration": "2108ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Wait till xpath=//XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "1408ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"Move to wishlist\"]", "status": "unknown", "duration": "12598ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "unknown", "duration": "2190ms", "action_id": "PH8FFnzohm", "screenshot_filename": "PH8FFnzohm.png", "report_screenshot": "PH8FFnzohm.png", "resolved_screenshot": "screenshots/PH8FFnzohm.png", "clean_action_id": "PH8FFnzohm", "prefixed_action_id": "al_PH8FFnzohm", "action_id_screenshot": "screenshots/PH8FFnzohm.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "status": "unknown", "duration": "2111ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "2303ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2575ms", "action_id": "WbxRVpWtjw", "screenshot_filename": "WbxRVpWtjw.png", "report_screenshot": "WbxRVpWtjw.png", "resolved_screenshot": "screenshots/WbxRVpWtjw.png", "clean_action_id": "WbxRVpWtjw", "prefixed_action_id": "al_WbxRVpWtjw", "action_id_screenshot": "screenshots/WbxRVpWtjw.png"}, {"name": "If exists: xpath=\"(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]\" (timeout: 10s) → Then tap on element with xpath: (//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "status": "unknown", "duration": "2268ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Tap on Text: \"Remove\"", "status": "unknown", "duration": "2546ms", "action_id": "EELcfo48Sh", "screenshot_filename": "EELcfo48Sh.png", "report_screenshot": "EELcfo48Sh.png", "resolved_screenshot": "screenshots/EELcfo48Sh.png", "clean_action_id": "EELcfo48Sh", "prefixed_action_id": "al_EELcfo48Sh", "action_id_screenshot": "screenshots/EELcfo48Sh.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "status": "unknown", "duration": "1587ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "unknown", "duration": "2470ms", "action_id": "nAB6Q8LAdv", "screenshot_filename": "nAB6Q8LAdv.png", "report_screenshot": "nAB6Q8LAdv.png", "resolved_screenshot": "screenshots/nAB6Q8LAdv.png", "clean_action_id": "nAB6Q8LAdv", "prefixed_action_id": "al_nAB6Q8LAdv", "action_id_screenshot": "screenshots/nAB6Q8LAdv.png"}, {"name": "Tap on element with xpath: //XCUIElementTypeButton[@name=\"txtSign out\"]", "status": "unknown", "duration": "10586ms", "action_id": "XCUIElemen", "screenshot_filename": "XCUIElemen.png", "report_screenshot": "XCUIElemen.png", "resolved_screenshot": "screenshots/XCUIElemen.png"}]}], "passed": 1, "failed": 0, "skipped": 0, "status": "passed"}
Action Log - 2025-06-16 09:56:24
================================================================================

[[09:56:24]] [INFO] Generating execution report...
[[09:56:24]] [SUCCESS] All tests passed successfully!
[[09:56:24]] [WARNING] Execution stopped by user.
[[09:56:24]] [SUCCESS] Screenshot refreshed
[[09:56:24]] [INFO] Refreshing screenshot...
[[09:56:23]] [SUCCESS] Screenshot refreshed successfully
[[09:56:23]] [SUCCESS] Screenshot refreshed
[[09:56:23]] [INFO] Refreshing screenshot...
[[09:56:16]] [WARNING] Stop requested. Finishing current action...
[[09:56:10]] [INFO] Executing action 18/39: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[3]//XCUIElementTypeLink)[1]
[[09:56:10]] [SUCCESS] Screenshot refreshed successfully
[[09:56:10]] [SUCCESS] Screenshot refreshed successfully
[[09:56:10]] [SUCCESS] Screenshot refreshed
[[09:56:10]] [INFO] Refreshing screenshot...
[[09:56:06]] [INFO] Executing action 17/39: Tap on image: deviceback-se.png
[[09:56:06]] [ERROR] Action 16 failed: Element not found or not tappable: xpath='//XCUIElementTypeButton[contains(@name,"to wishlist")]'
[[09:55:52]] [SUCCESS] Opened Appium Web Inspector with pre-filled connection details.
[[09:55:52]] [INFO] Opening URL: http://localhost:4723/inspector?host=127.0.0.1&port=4723&path=/wd/hub/
[[09:55:52]] [INFO] Found active session ID: 8826affb-6724-4982-8610-4548c31f7dd2. Inspector should list this session under 'Attach to Session'.
[[09:55:52]] [INFO] Opening Appium Web Inspector in a new window...
[[09:55:52]] [INFO] Checking Appium Web Inspector availability...
[[09:55:47]] [SUCCESS] Screenshot refreshed successfully
[[09:55:47]] [SUCCESS] Screenshot refreshed successfully
[[09:55:47]] [INFO] Executing action 16/39: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:55:46]] [SUCCESS] Screenshot refreshed
[[09:55:46]] [INFO] Refreshing screenshot...
[[09:55:42]] [INFO] Executing action 15/39: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:55:42]] [ERROR] Action 14 failed: Element not found or not tappable: xpath='(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]'
[[09:55:28]] [SUCCESS] Screenshot refreshed successfully
[[09:55:28]] [SUCCESS] Screenshot refreshed successfully
[[09:55:28]] [INFO] Executing action 14/39: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]/XCUIElementTypeOther[2]//XCUIElementTypeLink)[1]
[[09:55:27]] [SUCCESS] Screenshot refreshed
[[09:55:27]] [INFO] Refreshing screenshot...
[[09:55:24]] [SUCCESS] Screenshot refreshed successfully
[[09:55:24]] [SUCCESS] Screenshot refreshed successfully
[[09:55:23]] [INFO] Executing action 13/39: Tap on image: deviceback-se.png
[[09:55:23]] [SUCCESS] Screenshot refreshed
[[09:55:23]] [INFO] Refreshing screenshot...
[[09:55:19]] [SUCCESS] Screenshot refreshed successfully
[[09:55:19]] [SUCCESS] Screenshot refreshed successfully
[[09:55:19]] [INFO] Executing action 12/39: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"to wishlist")]
[[09:55:18]] [SUCCESS] Screenshot refreshed
[[09:55:18]] [INFO] Refreshing screenshot...
[[09:55:15]] [SUCCESS] Screenshot refreshed successfully
[[09:55:15]] [SUCCESS] Screenshot refreshed successfully
[[09:55:15]] [INFO] Executing action 11/39: Wait till xpath=//XCUIElementTypeStaticText[@name="SKU :"]
[[09:55:14]] [SUCCESS] Screenshot refreshed
[[09:55:14]] [INFO] Refreshing screenshot...
[[09:55:11]] [SUCCESS] Screenshot refreshed successfully
[[09:55:11]] [SUCCESS] Screenshot refreshed successfully
[[09:55:10]] [INFO] Executing action 10/39: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:55:10]] [SUCCESS] Screenshot refreshed
[[09:55:10]] [INFO] Refreshing screenshot...
[[09:55:07]] [SUCCESS] Screenshot refreshed successfully
[[09:55:07]] [SUCCESS] Screenshot refreshed successfully
[[09:55:06]] [INFO] Executing action 9/39: Wait till xpath=(//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]
[[09:55:06]] [SUCCESS] Screenshot refreshed
[[09:55:06]] [INFO] Refreshing screenshot...
[[09:55:03]] [SUCCESS] Screenshot refreshed successfully
[[09:55:03]] [SUCCESS] Screenshot refreshed successfully
[[09:55:03]] [INFO] Executing action 8/39: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[[09:55:02]] [SUCCESS] Screenshot refreshed
[[09:55:02]] [INFO] Refreshing screenshot...
[[09:54:58]] [SUCCESS] Screenshot refreshed successfully
[[09:54:58]] [SUCCESS] Screenshot refreshed successfully
[[09:54:58]] [INFO] Executing action 7/39: iOS Function: text
[[09:54:57]] [SUCCESS] Screenshot refreshed
[[09:54:57]] [INFO] Refreshing screenshot...
[[09:54:53]] [SUCCESS] Screenshot refreshed successfully
[[09:54:53]] [SUCCESS] Screenshot refreshed successfully
[[09:54:52]] [INFO] Executing action 6/39: Tap on Text: "Find"
[[09:54:52]] [SUCCESS] Screenshot refreshed
[[09:54:52]] [INFO] Refreshing screenshot...
[[09:54:51]] [SUCCESS] Screenshot refreshed
[[09:54:51]] [INFO] Refreshing screenshot...
[[09:54:48]] [INFO] Executing Multi Step action step 8/8: Check if element with xpath="//XCUIElementTypeStaticText[@name="txtHomeGreetingText"]" exists
[[09:54:47]] [SUCCESS] Screenshot refreshed successfully
[[09:54:47]] [SUCCESS] Screenshot refreshed successfully
[[09:54:46]] [SUCCESS] Screenshot refreshed
[[09:54:46]] [INFO] Refreshing screenshot...
[[09:54:41]] [SUCCESS] Screenshot refreshed successfully
[[09:54:41]] [SUCCESS] Screenshot refreshed successfully
[[09:54:41]] [INFO] Executing Multi Step action step 7/8: iOS Function: text
[[09:54:41]] [SUCCESS] Screenshot refreshed
[[09:54:41]] [INFO] Refreshing screenshot...
[[09:54:37]] [SUCCESS] Screenshot refreshed successfully
[[09:54:37]] [SUCCESS] Screenshot refreshed successfully
[[09:54:37]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //XCUIElementTypeSecureTextField[@name="Password"]
[[09:54:37]] [SUCCESS] Screenshot refreshed
[[09:54:37]] [INFO] Refreshing screenshot...
[[09:54:31]] [SUCCESS] Screenshot refreshed successfully
[[09:54:31]] [SUCCESS] Screenshot refreshed successfully
[[09:54:31]] [INFO] Executing Multi Step action step 5/8: Tap on Text: "Continue"
[[09:54:31]] [SUCCESS] Screenshot refreshed
[[09:54:31]] [INFO] Refreshing screenshot...
[[09:54:27]] [SUCCESS] Screenshot refreshed successfully
[[09:54:27]] [SUCCESS] Screenshot refreshed successfully
[[09:54:27]] [INFO] Executing Multi Step action step 4/8: Tap on image: keyboard_done_iphoneSE.png
[[09:54:27]] [SUCCESS] Screenshot refreshed
[[09:54:27]] [INFO] Refreshing screenshot...
[[09:54:24]] [SUCCESS] Screenshot refreshed successfully
[[09:54:24]] [SUCCESS] Screenshot refreshed successfully
[[09:54:23]] [INFO] Executing Multi Step action step 3/8: Input text: "<EMAIL>"
[[09:54:23]] [SUCCESS] Screenshot refreshed
[[09:54:23]] [INFO] Refreshing screenshot...
[[09:54:19]] [SUCCESS] Screenshot refreshed successfully
[[09:54:19]] [SUCCESS] Screenshot refreshed successfully
[[09:54:19]] [INFO] Executing Multi Step action step 2/8: Tap on element with xpath: //XCUIElementTypeTextField[@name="Email"]
[[09:54:19]] [SUCCESS] Screenshot refreshed
[[09:54:19]] [INFO] Refreshing screenshot...
[[09:54:15]] [SUCCESS] Screenshot refreshed successfully
[[09:54:15]] [SUCCESS] Screenshot refreshed successfully
[[09:54:15]] [INFO] Executing Multi Step action step 1/8: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:54:15]] [INFO] Loaded 8 steps from test case: Kmart-NZ-Signin
[[09:54:15]] [INFO] Loading steps for Multi Step action: Kmart-NZ-Signin
[[09:54:15]] [INFO] Executing action 5/39: Execute Test Case: Kmart-NZ-Signin (8 steps)
[[09:54:15]] [SUCCESS] Screenshot refreshed
[[09:54:15]] [INFO] Refreshing screenshot...
[[09:54:12]] [SUCCESS] Screenshot refreshed successfully
[[09:54:12]] [SUCCESS] Screenshot refreshed successfully
[[09:54:12]] [INFO] Executing action 4/39: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[[09:54:11]] [SUCCESS] Screenshot refreshed
[[09:54:11]] [INFO] Refreshing screenshot...
[[09:54:09]] [SUCCESS] Screenshot refreshed successfully
[[09:54:09]] [SUCCESS] Screenshot refreshed successfully
[[09:54:08]] [INFO] Executing action 3/39: iOS Function: alert_accept
[[09:54:08]] [SUCCESS] Screenshot refreshed
[[09:54:08]] [INFO] Refreshing screenshot...
[[09:54:03]] [SUCCESS] Screenshot refreshed successfully
[[09:54:03]] [SUCCESS] Screenshot refreshed successfully
[[09:54:02]] [INFO] Executing action 2/39: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[09:54:01]] [SUCCESS] Screenshot refreshed
[[09:54:01]] [INFO] Refreshing screenshot...
[[09:53:56]] [INFO] Executing action 1/39: Restart app: nz.com.kmart
[[09:53:56]] [INFO] ExecutionManager: Starting execution of 39 actions...
[[09:53:56]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[09:53:56]] [INFO] Clearing screenshots from database before execution...
[[09:53:56]] [SUCCESS] All screenshots deleted successfully
[[09:53:56]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[09:53:56]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_095356/screenshots
[[09:53:56]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250616_095356
[[09:53:56]] [SUCCESS] Report directory initialized successfully
[[09:53:56]] [INFO] Initializing report directory and screenshots folder...
[[09:53:53]] [SUCCESS] All screenshots deleted successfully
[[09:53:53]] [SUCCESS] Loaded test case "WishList NZ" with 39 actions
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: swipe
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tapOnText
[[09:53:53]] [SUCCESS] Added action: ifElseSteps
[[09:53:53]] [SUCCESS] Added action: tapOnText
[[09:53:53]] [SUCCESS] Added action: ifElseSteps
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tapOnText
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tapOnText
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: iosFunctions
[[09:53:53]] [SUCCESS] Added action: tapOnText
[[09:53:53]] [SUCCESS] Added action: multiStep
[[09:53:53]] [SUCCESS] Added action: waitTill
[[09:53:53]] [SUCCESS] Added action: iosFunctions
[[09:53:53]] [SUCCESS] Added action: tap
[[09:53:53]] [SUCCESS] Added action: restartApp
[[09:53:53]] [INFO] All actions cleared
[[09:53:53]] [INFO] Cleaning up screenshots...
[[09:53:47]] [SUCCESS] Screenshot refreshed successfully
[[09:53:46]] [SUCCESS] Screenshot refreshed
[[09:53:46]] [INFO] Refreshing screenshot...
[[09:53:45]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[09:53:45]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[09:53:42]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[09:53:40]] [SUCCESS] Found 1 device(s)
[[09:53:40]] [INFO] Refreshing device list...
